<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
    <data name="Connect" xml:space="preserve">
        <value>Connect</value>
    </data>
    <data name="Save" xml:space="preserve">
        <value>Save</value>
    </data>
    <data name="FromDate" xml:space="preserve">
        <value>From date</value>
    </data>
    <data name="ToDate" xml:space="preserve">
        <value>To date</value>
    </data>
    <data name="FromInvoiceNumber" xml:space="preserve">
        <value>From invoice number</value>
    </data>
    <data name="ToInvoiceNumber" xml:space="preserve">
        <value>To invoice number</value>
    </data>
    <data name="OutgoingInvoice" xml:space="preserve">
        <value>Outgoing invoice</value>
    </data>
    <data name="IncomingInvoice" xml:space="preserve">
        <value>Incoming invoice</value>
    </data>
    <data name="EInvoices" xml:space="preserve">
        <value>E-Invoices</value>
    </data>
    <data name="GrainAccounting" xml:space="preserve">
        <value>Grain accounting</value>
    </data>
    <data name="Search" xml:space="preserve">
        <value>Search</value>
    </data>
    <data name="DeactivatePaging" xml:space="preserve">
        <value>Deactivate paging</value>
    </data>
    <data name="ActivatePaging" xml:space="preserve">
        <value>Activate paging</value>
    </data>
    <data name="PagingSummaryFormat" xml:space="preserve">
        <value>Page {0} of {1} ({2} entries)</value>
    </data>
    <data name="GroupPanelText" xml:space="preserve">
        <value>Drag a column header here and release it to group by this column</value>
    </data>
    <data name="SelectAllItems" xml:space="preserve">
        <value>Select all items</value>
    </data>
    <data name="SelectItem" xml:space="preserve">
        <value>Select item</value>
    </data>
    <data name="InvoiceNumber" xml:space="preserve">
        <value>Invoice number</value>
    </data>
    <data name="Total" xml:space="preserve">
        <value>Total</value>
    </data>
    <data name="Type" xml:space="preserve">
        <value>Type</value>
    </data>
    <data name="Date" xml:space="preserve">
        <value>Date</value>
    </data>
    <data name="CreationDate" xml:space="preserve">
        <value>Creation date</value>
    </data>
    <data name="TotalAmount" xml:space="preserve">
        <value>Total amount</value>
    </data>
    <data name="Email" xml:space="preserve">
        <value>E-Mail</value>
    </data>
    <data name="EInvoice" xml:space="preserve">
        <value>E-Invoice</value>
    </data>
    <data name="Exported" xml:space="preserve">
        <value>Exported</value>
    </data>
    <data name="EmailSent" xml:space="preserve">
        <value>E-Mail sent</value>
    </data>
    <data name="Invoices" xml:space="preserve">
        <value>Invoices</value>
    </data>
    <data name="Close" xml:space="preserve">
        <value>Close</value>
    </data>
    <data name="Export" xml:space="preserve">
        <value>Export</value>
    </data>
    <data name="Taxes" xml:space="preserve">
        <value>Taxes</value>
    </data>
    <data name="Positions" xml:space="preserve">
        <value>Positions</value>
    </data>
    <data name="Invoicing" xml:space="preserve">
        <value>Invoicing</value>
    </data>
    <data name="InvoiceOverview" xml:space="preserve">
        <value>Invoice overview</value>
    </data>
    <data name="CancellationNumber" xml:space="preserve">
        <value>Cancellation number</value>
    </data>
    <data name="EmailForEInvoice" xml:space="preserve">
        <value>E-Mail for E-Invoice</value>
    </data>
    <data name="EmailPreview" xml:space="preserve">
        <value>E-Mail preview</value>
    </data>
    <data name="SearchParameters" xml:space="preserve">
        <value>Search parameters</value>
    </data>
    <data name="PDFViewer" xml:space="preserve">
        <value>PDF viewer</value>
    </data>
    <data name="OwnStepText" xml:space="preserve">
        <value>{0} of {1} invoices processed!</value>
    </data>
    <data name="OutgoingInvoiceInformation" xml:space="preserve">
        <value>Outgoing invoice information</value>
    </data>
    <data name="OpenDesigner" xml:space="preserve">
        <value>Open Designer</value>
    </data>
    <data name="OpenInvoice" xml:space="preserve">
        <value>Open invoice</value>
    </data>
    <data name="NoInvoicesFound" xml:space="preserve">
        <value>No invoices found</value>
    </data>
    <data name="ErrorWhenOpeningThePDF" xml:space="preserve">
        <value>Error when opening the PDF</value>
    </data>
    <data name="CancellationInvoiceNumber" xml:space="preserve">
        <value>Cancellation invoice number</value>
    </data>
    <data name="InvoiceHolder" xml:space="preserve">
        <value>Invoice holder</value>
    </data>
    <data name="InvoiceDate" xml:space="preserve">
        <value>Invoice date</value>
    </data>
    <data name="InvoiceIssuer" xml:space="preserve">
        <value>Invoice issuer</value>
    </data>
    <data name="OriginalInvoiceNumber" xml:space="preserve">
        <value>Original invoice number</value>
    </data>
    <data name="TotalGross" xml:space="preserve">
        <value>Total gross</value>
    </data>
    <data name="TotalNet" xml:space="preserve">
        <value>Total net</value>
    </data>
    <data name="TotalTaxes" xml:space="preserve">
        <value>Total taxes</value>
    </data>
    <data name="ValueDate" xml:space="preserve">
        <value>Value date</value>
    </data>
    <data name="AutoLogin" xml:space="preserve">
        <value>Auto login</value>
    </data>
    <data name="ClientDatabase" xml:space="preserve">
        <value>Client database</value>
    </data>
    <data name="ClientNo" xml:space="preserve">
        <value>Client no.</value>
    </data>
    <data name="DatabaseName" xml:space="preserve">
        <value>Database name</value>
    </data>
    <data name="DatabasePassword" xml:space="preserve">
        <value>Database password</value>
    </data>
    <data name="DatabasePath" xml:space="preserve">
        <value>Database path</value>
    </data>
    <data name="DatabaseUsername" xml:space="preserve">
        <value>Database username</value>
    </data>
    <data name="ManageConnection" xml:space="preserve">
        <value>Manage connection</value>
    </data>
    <data name="MasterDatabase" xml:space="preserve">
        <value>Master database</value>
    </data>
    <data name="MasterDatabaseNotReachable" xml:space="preserve">
        <value>Master database is not reachable: {0}</value>
    </data>
    <data name="NoDatabaseExistsForClient" xml:space="preserve">
        <value>No database exists for this client({0}).</value>
    </data>
    <data name="OpenConfigurationFolder" xml:space="preserve">
        <value>Open configuration folder</value>
    </data>
    <data name="WarehouseDatabase" xml:space="preserve">
        <value>Warehouse database</value>
    </data>
    <data name="Address" xml:space="preserve">
        <value>Address</value>
    </data>
    <data name="Name" xml:space="preserve">
        <value>Name</value>
    </data>
    <data name="User" xml:space="preserve">
        <value>User</value>
    </data>
    <data name="Percent" xml:space="preserve">
        <value>Percent</value>
    </data>
    <data name="TaxAmount" xml:space="preserve">
        <value>Tax amount</value>
    </data>
    <data name="NetAmount" xml:space="preserve">
        <value>Net amount</value>
    </data>
    <data name="Position" xml:space="preserve">
        <value>Position</value>
    </data>
  <data name="ArticleNumber" xml:space="preserve">
      <value>Article number</value>
  </data>
    <data name="Description" xml:space="preserve">
        <value>Description</value>
    </data>
    <data name="TotalDiscount" xml:space="preserve">
        <value>Total discount</value>
    </data>
    <data name="TotalNetWithDiscount" xml:space="preserve">
        <value>Total net with discount</value>
    </data>
    <data name="Account" xml:space="preserve">
        <value>Account</value>
    </data>
    <data name="DeliveryNoteNumber" xml:space="preserve">
        <value>Delivery note number</value>
    </data>
    <data name="No" xml:space="preserve">
        <value>No</value>
    </data>
    <data name="GrainAccountingInformation" xml:space="preserve">
        <value>Grain accounting information</value>
    </data>
    <data name="IncomingInvoiceInformation" xml:space="preserve">
        <value>Incoming invoice information</value>
    </data>
    <data name="VAT" xml:space="preserve">
        <value>VAT</value>
    </data>
    <data name="VATAmount" xml:space="preserve">
        <value>VAT amount</value>
    </data>
    <data name="TotalVAT" xml:space="preserve">
        <value>Total VAT</value>
    </data>
    <data name="Ausgangsrechnung" xml:space="preserve">
        <value>Outgoing invoice</value>
    </data>
    <data name="Eingangsrechnung" xml:space="preserve">
        <value>Incoming invoice</value>
    </data>
    <data name="Getreideabrechnung" xml:space="preserve">
        <value>Grain accounting</value>
    </data>
    <data name="MasterData" xml:space="preserve">
        <value>Master data</value>
    </data>
    <data name="CustomerData" xml:space="preserve">
        <value>Customer data</value>
    </data>
    <data name="CompanyMasterData" xml:space="preserve">
        <value>Company master data</value>
    </data>
    <data name="ProcessArticles" xml:space="preserve">
        <value>Process articles</value>
    </data>
    <data name="BasicData" xml:space="preserve">
        <value>Basic data</value>
    </data>
    <data name="CustomerNo" xml:space="preserve">
        <value>Customer no.</value>
    </data>
    <data name="Matchcode" xml:space="preserve">
        <value>Matchcode</value>
    </data>
    <data name="Salutation" xml:space="preserve">
        <value>Salutation</value>
    </data>
    <data name="ContactPerson" xml:space="preserve">
        <value>Contact person</value>
    </data>
    <data name="Street" xml:space="preserve">
        <value>Street</value>
    </data>
    <data name="ZipCodeCity" xml:space="preserve">
        <value>Zip code / City</value>
    </data>
    <data name="Country" xml:space="preserve">
        <value>Country</value>
    </data>
    <data name="FederalState" xml:space="preserve">
        <value>Federal state</value>
    </data>
    <data name="Communication" xml:space="preserve">
        <value>Communication</value>
    </data>
    <data name="Phone" xml:space="preserve">
        <value>Phone</value>
    </data>
    <data name="Fax" xml:space="preserve">
        <value>Fax</value>
    </data>
    <data name="Internet" xml:space="preserve">
        <value>Internet</value>
    </data>
    <data name="IdentNumber" xml:space="preserve">
        <value>Ident number</value>
    </data>
    <data name="ILN" xml:space="preserve">
        <value>ILN</value>
    </data>
    <data name="Kdleh" xml:space="preserve">
        <value>Kdleh</value>
    </data>
    <data name="CustomerSearch" xml:space="preserve">
        <value>Customer search</value>
    </data>
    <data name="SearchFor" xml:space="preserve">
        <value>Search</value>
    </data>
    <data name="NoCustomersFound" xml:space="preserve">
        <value>No customers found</value>
    </data>
    <data name="Number" xml:space="preserve">
        <value>Number</value>
    </data>
    <data name="ZipCode" xml:space="preserve">
        <value>Zip code</value>
    </data>
    <data name="City" xml:space="preserve">
        <value>City</value>
    </data>
    <data name="POBox" xml:space="preserve">
        <value>P.O. Box</value>
    </data>
    <data name="Identifier" xml:space="preserve">
        <value>Identifier</value>
    </data>
    <data name="Customers" xml:space="preserve">
        <value>Customers</value>
    </data>
    <data name="New" xml:space="preserve">
        <value>New</value>
    </data>
    <data name="Delete" xml:space="preserve">
        <value>Delete</value>
    </data>
    <data name="BasicArticle" xml:space="preserve">
        <value>Basic article</value>
    </data>
    <data name="SelectAMainArticle" xml:space="preserve">
        <value>Select a main article</value>
    </data>
    <data name="ReferenceNumber" xml:space="preserve">
        <value>Reference number</value>
    </data>
    <data name="Bag" xml:space="preserve">
        <value>Bag</value>
    </data>
    <data name="EanBag" xml:space="preserve">
        <value>Ean bag</value>
    </data>
    <data name="EanBagIsMandatory" xml:space="preserve">
        <value>Ean bag is mandatory</value>
    </data>
    <data name="LabelType" xml:space="preserve">
        <value>Label type</value>
    </data>
    <data name="LabelTypeIsMandatory" xml:space="preserve">
        <value>Label type is mandatory</value>
    </data>
    <data name="BagType" xml:space="preserve">
        <value>Bag type</value>
    </data>
    <data name="BagCount" xml:space="preserve">
        <value>Bag count</value>
    </data>
    <data name="BagCountIsMandatory" xml:space="preserve">
        <value>Bag count is mandatory</value>
    </data>
    <data name="BagContent" xml:space="preserve">
        <value>Bag content</value>
    </data>
    <data name="BagContentIsMandatory" xml:space="preserve">
        <value>Bag content is mandatory</value>
    </data>
    <data name="PrintLayoutForBagLabel" xml:space="preserve">
        <value>Print layout for bag label</value>
    </data>
    <data name="SelectPrintLayout" xml:space="preserve">
        <value>Select print layout</value>
    </data>
    <data name="Dimensions" xml:space="preserve">
        <value>Dimensions</value>
    </data>
    <data name="HeightCm" xml:space="preserve">
        <value>Height (cm)</value>
    </data>
    <data name="WidthCm" xml:space="preserve">
        <value>Width (cm)</value>
    </data>
    <data name="DepthCm" xml:space="preserve">
        <value>Depth (cm)</value>
    </data>
    <data name="Note" xml:space="preserve">
        <value>Note</value>
    </data>
    <data name="DescriptionIsMandatory" xml:space="preserve">
        <value>Description is mandatory</value>
    </data>
    <data name="Pallet" xml:space="preserve">
        <value>Pallet</value>
    </data>
    <data name="EanPallet" xml:space="preserve">
        <value>Ean pallet</value>
    </data>
    <data name="EanPalletIsMandatory" xml:space="preserve">
        <value>Ean pallet is mandatory</value>
    </data>
    <data name="PalletType" xml:space="preserve">
        <value>Pallet type</value>
    </data>
    <data name="PalletizerRecipe" xml:space="preserve">
        <value>Palletizer recipe</value>
    </data>
    <data name="PrintCount" xml:space="preserve">
        <value>Print count</value>
    </data>
    <data name="PrintCountIsMandatory" xml:space="preserve">
        <value>Print count is mandatory</value>
    </data>
    <data name="PrintLayoutForPalletLabel" xml:space="preserve">
        <value>Print layout for pallet label</value>
    </data>
    <data name="AdditionalData" xml:space="preserve">
        <value>Additional data</value>
    </data>
    <data name="BestBeforeDateMonth" xml:space="preserve">
        <value>Best before date (month)</value>
    </data>
    <data name="BestBeforeDateMonthIsMandatory" xml:space="preserve">
        <value>Best before date (month) is mandatory</value>
    </data>
    <data name="NoResults" xml:space="preserve">
        <value>No results</value>
    </data>
    <data name="EasiExport" xml:space="preserve">
        <value>Easi Export</value>
    </data>
    <data name="EasiAdfinityInterface" xml:space="preserve">
        <value>Easi Adfinity interface</value>
    </data>
    <data name="APIParameters" xml:space="preserve">
        <value>API parameters</value>
    </data>
    <data name="WeighingSlip" xml:space="preserve">
        <value>Weighing slip</value>
    </data>
    <data name="Configuration" xml:space="preserve">
        <value>Configuration</value>
    </data>
    <data name="Connection" xml:space="preserve">
        <value>Connection</value>
    </data>
    <data name="Username" xml:space="preserve">
        <value>Username</value>
    </data>
    <data name="Password" xml:space="preserve">
        <value>Password</value>
    </data>
    <data name="BaseURL" xml:space="preserve">
        <value>Base URL</value>
    </data>
    <data name="AdfinityDatabase" xml:space="preserve">
        <value>Adfinity database</value>
    </data>
    <data name="AdfinityEnvir" xml:space="preserve">
        <value>Adfinity Envir</value>
    </data>
    <data name="ArticleReferenceSize" xml:space="preserve">
        <value>Article reference size</value>
    </data>
    <data name="Assignments" xml:space="preserve">
        <value>Assignments</value>
    </data>
    <data name="SaveConfiguration" xml:space="preserve">
        <value>Save configuration</value>
    </data>
    <data name="ElectronicWgsConfiguration" xml:space="preserve">
        <value>Electronic Weighing Slip Configuration</value>
    </data>
    <data name="ElectronicWgs" xml:space="preserve">
        <value>Electronic Weighing Slip</value>
    </data>
    <data name="FromWeighingSlipNumber" xml:space="preserve">
        <value>From weighing slip number</value>
    </data>
    <data name="ToWeighingSlipNumber" xml:space="preserve">
        <value>To weighing slip number</value>
    </data>
    <data name="ReExportExportedWeighingSlips" xml:space="preserve">
        <value>Re-export exported weighing slips</value>
    </data>
    <data name="Send" xml:space="preserve">
        <value>Send</value>
    </data>
    <data name="CreateJob" xml:space="preserve">
        <value>Create job</value>
    </data>
    <data name="Error" xml:space="preserve">
        <value>Error</value>
    </data>
    <data name="NoWeighingSlipsFound" xml:space="preserve">
        <value>No weighing slips found</value>
    </data>
    <data name="CountOfWeighingSlips" xml:space="preserve">
        <value>Count of weighing slips</value>
    </data>
    <data name="SuccessfullySent" xml:space="preserve">
        <value>Successfully sent</value>
    </data>
    <data name="Duration" xml:space="preserve">
        <value>Duration</value>
    </data>
    <data name="ReExportExportedInvoices" xml:space="preserve">
        <value>Re-export exported invoices</value>
    </data>
    <data name="CountOfInvoices" xml:space="preserve">
        <value>Count of invoices</value>
    </data>
    <data name="ServerName" xml:space="preserve">
        <value>Server name</value>
    </data>
    <data name="NameIsMandatory" xml:space="preserve">
        <value>Name is mandatory</value>
    </data>
    <data name="TimeInterval" xml:space="preserve">
        <value>Time interval</value>
    </data>
    <data name="TimeIntervalIsMandatory" xml:space="preserve">
        <value>Time interval is mandatory</value>
    </data>
    <data name="Create" xml:space="preserve">
        <value>Create</value>
    </data>
    <data name="Tasks" xml:space="preserve">
        <value>Tasks</value>
    </data>
    <data name="Jobs" xml:space="preserve">
        <value>Jobs</value>
    </data>
    <data name="Reload" xml:space="preserve">
        <value>Reload</value>
    </data>
    <data name="Status" xml:space="preserve">
        <value>Status</value>
    </data>
    <data name="Data" xml:space="preserve">
        <value>Data</value>
    </data>
    <data name="Server" xml:space="preserve">
        <value>Server</value>
    </data>
    <data name="StartTime" xml:space="preserve">
        <value>Start time</value>
    </data>
    <data name="NextTriggerTime" xml:space="preserve">
        <value>Next trigger time</value>
    </data>
    <data name="Execute" xml:space="preserve">
        <value>Execute</value>
    </data>
    <data name="Stop" xml:space="preserve">
        <value>Stop</value>
    </data>
    <data name="Start" xml:space="preserve">
        <value>Start</value>
    </data>
    <data name="Logging" xml:space="preserve">
        <value>Logging</value>
    </data>
    <data name="CreateTask" xml:space="preserve">
        <value>Create task</value>
    </data>
    <data name="Normal" xml:space="preserve">
        <value>Normal</value>
    </data>
    <data name="Paused" xml:space="preserve">
        <value>Paused</value>
    </data>
    <data name="Completed" xml:space="preserve">
        <value>Completed</value>
    </data>
    <data name="Blocked" xml:space="preserve">
        <value>Blocked</value>
    </data>
    <data name="NotAvailable" xml:space="preserve">
        <value>Not available</value>
    </data>
    <data name="Unknown" xml:space="preserve">
        <value>Unknown</value>
    </data>
    <data name="Logs" xml:space="preserve">
        <value>Logs</value>
    </data>
    <data name="ComboBoxes" xml:space="preserve">
        <value>Combo boxes</value>
    </data>
    <data name="ComboBoxesConfiguration" xml:space="preserve">
        <value>Combo boxes configuration</value>
    </data>
    <data name="Index" xml:space="preserve">
        <value>Index</value>
    </data>
    <data name="CreateComboBox" xml:space="preserve">
        <value>Create combo box</value>
    </data>
    <data name="EditComboBox" xml:space="preserve">
        <value>Edit combo box</value>
    </data>
    <data name="ElectronicInvoice" xml:space="preserve">
        <value>Electronic invoice</value>
    </data>
    <data name="ElectronicInvoiceConfiguration" xml:space="preserve">
        <value>Electronic invoice configuration</value>
    </data>
    <data name="SenderName" xml:space="preserve">
        <value>Sender name</value>
    </data>
    <data name="SenderEmail" xml:space="preserve">
        <value>Sender E-Mail</value>
    </data>
    <data name="Port" xml:space="preserve">
        <value>Port</value>
    </data>
    <data name="TimeoutInMilliseconds" xml:space="preserve">
        <value>Timeout in milliseconds</value>
    </data>
    <data name="PrintLayout" xml:space="preserve">
        <value>Print layout</value>
    </data>
    <data name="OpenDesignerForEInvoice" xml:space="preserve">
        <value>Open designer for e-invoice</value>
    </data>
    <data name="SendTestEmail" xml:space="preserve">
        <value>Send test E-Mail</value>
    </data>
    <data name="DeleteEmailConfiguration" xml:space="preserve">
        <value>Delete E-Mail configuration</value>
    </data>
    <data name="Subject" xml:space="preserve">
        <value>Subject</value>
    </data>
    <data name="FileName" xml:space="preserve">
        <value>File name</value>
    </data>
    <data name="SelectCountry" xml:space="preserve">
        <value>Select country</value>
    </data>
    <data name="EmailCouldNotBeCreated" xml:space="preserve">
        <value>E-Mail could not be created</value>
    </data>
    <data name="EmailTestPreview" xml:space="preserve">
        <value>E-Mail test preview</value>
    </data>
    <data name="To" xml:space="preserve">
        <value>To</value>
    </data>
    <data name="Attachment" xml:space="preserve">
        <value>Attachment</value>
    </data>
    <data name="APIKeys" xml:space="preserve">
        <value>API keys</value>
    </data>
    <data name="Key" xml:space="preserve">
        <value>Key</value>
    </data>
    <data name="AddAPIKey" xml:space="preserve">
        <value>Add API key</value>
    </data>
    <data name="License" xml:space="preserve">
        <value>License</value>
    </data>
    <data name="AddFromClipboard" xml:space="preserve">
        <value>Add from clipboard</value>
    </data>
    <data name="LicenseData" xml:space="preserve">
        <value>License data</value>
    </data>
    <data name="LicenseInformation" xml:space="preserve">
        <value>License information</value>
    </data>
    <data name="ValidUntil" xml:space="preserve">
        <value>Valid until</value>
    </data>
    <data name="Modules" xml:space="preserve">
        <value>Modules</value>
    </data>
    <data name="Licenses" xml:space="preserve">
        <value>Licenses</value>
    </data>
    <data name="InvalidLicenseDataError" xml:space="preserve">
        <value>The input is not a valid base-64 string because it contains a non-base-64 character, more than two padding characters or an invalid character among the padding characters.</value>
    </data>
  <data name="Language" xml:space="preserve">
      <value>Language</value>
  </data>
  <data name="ZUGFeRD" xml:space="preserve">
      <value>ZUGFeRD</value>
  </data>
  <data name="ZUGFeRDXRechnung" xml:space="preserve">
      <value>ZUGFeRD (XRechnung)</value>
  </data>
  <data name="XRechnung" xml:space="preserve">
      <value>XRechnung</value>
  </data>
  <data name="EDIFACTTrueCommerce" xml:space="preserve">
      <value>EDIFACT (TrueCommerce)</value>
  </data>
  <data name="EDIFACTStradEdi" xml:space="preserve">
      <value>EDIFACT (StradEdi)</value>
  </data>
  <data name="DatabaseIsBeingLoaded" xml:space="preserve">
      <value>Database is being loaded</value>
  </data>
  <data name="ProcessArticleSearch" xml:space="preserve">
      <value>Process article search</value>
  </data>
  <data name="NoOrderFound" xml:space="preserve">
      <value>No order found</value>
  </data>
  <data name="Orders" xml:space="preserve">
      <value>Orders</value>
  </data>
  <data name="WithCustomer" xml:space="preserve">
      <value>With customer</value>
  </data>
  <data name="WithoutCustomer" xml:space="preserve">
      <value>Without customer</value>
  </data>
  <data name="OnlyCustomer" xml:space="preserve">
      <value>Only customer</value>
  </data>
  <data name="CommissionTrack" xml:space="preserve">
      <value>Commission track</value>
  </data>
  <data name="Order" xml:space="preserve">
      <value>Order</value>
  </data>
  <data name="PrintLayoutForDeliveryNote" xml:space="preserve">
      <value>Print Layout Deliverynote</value>
  </data>
  <data name="BeginningExportDate" xml:space="preserve">
      <value>Beginning of export date</value>
  </data>
  <data name="PrintLayoutForPalletNote" xml:space="preserve">
      <value>Print layout for Palletnot</value>
  </data>
  <data name="DATEVExport" xml:space="preserve">
      <value>DATEV export</value>
  </data>
  <data name="ExportParameters" xml:space="preserve">
      <value>Export parameters</value>
  </data>
  <data name="StartOfFinancialYear" xml:space="preserve">
      <value>Start of financial year</value>
  </data>
  <data name="ConsultantNumber" xml:space="preserve">
      <value>Consultant number</value>
  </data>
  <data name="GeneralLedgerAccountLength" xml:space="preserve">
      <value>General ledger account length</value>
  </data>
  <data name="SuffixForAccounts" xml:space="preserve">
      <value>Suffix for accounts</value>
  </data>
  <data name="FilePath" xml:space="preserve">
      <value>File path</value>
  </data>
  <data name="UseExternalInvoiceNumberForIncomingInvoice" xml:space="preserve">
      <value>Use external invoice number for incoming invoice</value>
  </data>
  <data name="ExportOfInvoicesSuccessfullyCompleted" xml:space="preserve">
      <value>The export of {0} invoices has been successfully completed. Duration: {1}.</value>
  </data>
  <data name="GeneralLedgerAccountLengthError" xml:space="preserve">
      <value>General ledger account length must not be greater than 9 and less than 4.</value>
  </data>
  <data name="StartOfFinancialYearFirstDayOfMonthError" xml:space="preserve">
      <value>The start of the financial year must always be the first day of the month.</value>
  </data>
  <data name="EDIFACTExport" xml:space="preserve">
      <value>EDIFACT export</value>
  </data>
  <data name="ADDISONAKTEtsenitExport" xml:space="preserve">
      <value>ADDISON AKTE (tse:nit) export</value>
  </data>
  <data name="FromCreationDate" xml:space="preserve">
      <value>From creation date</value>
  </data>
  <data name="FromInvoiceDate" xml:space="preserve">
      <value>From invoice date</value>
  </data>
  <data name="UntilCreationDate" xml:space="preserve">
      <value>Until creation date</value>
  </data>
  <data name="UntilInvoiceDate" xml:space="preserve">
      <value>Until invoice date</value>
  </data>
  <data name="UntilInvoiceNumber" xml:space="preserve">
      <value>Until invoice number</value>
  </data>
  <data name="ADDISONClientNumber" xml:space="preserve">
      <value>ADDISON client number</value>
  </data>
  <data name="UseInvoiceDateInsteadOfSystemDateForExport" xml:space="preserve">
      <value>Use invoice date instead of system date for export</value>
  </data>
  <data name="WithSubsequentBooking" xml:space="preserve">
      <value>With subsequent booking</value>
  </data>
  <data name="SubsequentPostingsForVATAreGeneratedAutomatically" xml:space="preserve">
      <value>Subsequent postings for VAT (actual VAT and EC acquisition) are generated automatically</value>
  </data>
  <data name="ADDISONPipelineExport" xml:space="preserve">
      <value>ADDISON Pipeline export</value>
  </data>
  <data name="FileNameWithTimestamp" xml:space="preserve">
      <value>File name with timestamp</value>
  </data>
  <data name="EDIFACT" xml:space="preserve">
      <value>EDIFACT</value>
  </data>
  <data name="DATEV" xml:space="preserve">
      <value>DATEV</value>
  </data>
  <data name="ADDISONAKTEtsenit" xml:space="preserve">
      <value>ADDISON AKTE (tse:nit)</value>
  </data>
  <data name="ADDISONPipeline" xml:space="preserve">
      <value>ADDISON Pipeline</value>
  </data>
  <data name="EasiAdfinity" xml:space="preserve">
      <value>Easi Adfinity</value>
  </data>
  <data name="SearchForSuppliers" xml:space="preserve">
      <value>Search for suppliers</value>
  </data>
  <data name="SearchAccounting" xml:space="preserve">
      <value>Search accounting</value>
  </data>
  <data name="LoadWeighingSlips" xml:space="preserve">
      <value>Load weighing slips</value>
  </data>
  <data name="ContinueAccounting" xml:space="preserve">
      <value>Continue accounting</value>
  </data>
  <data name="Settings" xml:space="preserve">
      <value>Settings</value>
  </data>
  <data name="NoSupplierFound" xml:space="preserve">
      <value>No supplier found</value>
  </data>
  <data name="NoSupplierFoundForSearchTerm" xml:space="preserve">
      <value>No suitable supplier was found for the search term. Please check your entry.</value>
  </data>
  <data name="PleaseEnterAValidAccountingNumber" xml:space="preserve">
      <value>Please enter a valid accounting number.</value>
  </data>
  <data name="EnterAccountingNumber" xml:space="preserve">
      <value>Enter accounting number</value>
  </data>
  <data name="WeighingSlipNumber" xml:space="preserve">
      <value>Slip number</value>
  </data>
  <data name="SupplierNumber" xml:space="preserve">
      <value>Supplier number</value>
  </data>
  <data name="Article" xml:space="preserve">
      <value>Article</value>
  </data>
  <data name="Weight" xml:space="preserve">
      <value>Weight</value>
  </data>
  <data name="Apply" xml:space="preserve">
      <value>Apply</value>
  </data>
  <data name="InconsistentSelection" xml:space="preserve">
      <value>Inconsistent selection</value>
  </data>
  <data name="AllSelectedItemsSameWeighingSlipSupplierNumber" xml:space="preserve">
      <value>All selected items must have the same weighing slip supplier number.</value>
  </data>
  <data name="SelectionError" xml:space="preserve">
      <value>Selection error</value>
  </data>
  <data name="OnlySelectItemsWithSameWeighingSlipSupplierNumber" xml:space="preserve">
      <value>You can only select items with the same weighing slip supplier number.</value>
  </data>
  <data name="NoSelection" xml:space="preserve">
      <value>No selection</value>
  </data>
  <data name="SelectAtLeastOneWeighingSlip" xml:space="preserve">
      <value>Please select at least one weighing slip.</value>
  </data>
  <data name="WeighingSlipsUsedForBillingConfirmation" xml:space="preserve">
      <value>{0} Weighing slips are used for billing. Would you like to continue?</value>
  </data>
  <data name="Confirmation" xml:space="preserve">
      <value>Confirmation</value>
  </data>
  <data name="Yes" xml:space="preserve">
      <value>Yes</value>
  </data>
  <data name="AnErrorOccuredWhenCreatingTheInvoice" xml:space="preserve">
      <value>An error occurred when creating the invoice: {0}</value>
  </data>
  <data name="AccountingInformationIsLoaded" xml:space="preserve">
      <value>Accounting information is loaded.</value>
  </data>
  <data name="ViewModeReadOnly" xml:space="preserve">
      <value>View mode (read-only).</value>
  </data>
  <data name="GrainAccountingAlreadyEstablished" xml:space="preserve">
      <value>Grain accounting already established</value>
  </data>
  <data name="YouMustDeleteTheEntryToChangeAnything" xml:space="preserve">
      <value>You must delete the entry to change anything</value>
  </data>
  <data name="EditModeActive" xml:space="preserve">
      <value>Edit mode active</value>
  </data>
  <data name="ShowLess" xml:space="preserve">
      <value>Show less</value>
  </data>
  <data name="ShowMore" xml:space="preserve">
      <value>Show more</value>
  </data>
  <data name="Supplier" xml:space="preserve">
      <value>Supplier</value>
  </data>
  <data name="InvoiceOffice" xml:space="preserve">
      <value>Invoice office</value>
  </data>
  <data name="Cancel" xml:space="preserve">
      <value>Cancel</value>
  </data>
  <data name="Edit" xml:space="preserve">
      <value>Edit</value>
  </data>
  <data name="DeleteWeighingSlip" xml:space="preserve">
      <value>Delete weighing slip</value>
  </data>
  <data name="Contract" xml:space="preserve">
      <value>Contract</value>
  </data>
  <data name="NoContract" xml:space="preserve">
      <value>No contract</value>
  </data>
  <data name="Designation" xml:space="preserve">
      <value>Designation</value>
  </data>
  <data name="LaboratoryValue" xml:space="preserve">
      <value>Laboratory value</value>
  </data>
  <data name="UnitPrice" xml:space="preserve">
      <value>Unit price</value>
  </data>
  <data name="TotalPrice" xml:space="preserve">
      <value>Total price</value>
  </data>
  <data name="ResetValue" xml:space="preserve">
      <value>Reset value</value>
  </data>
  <data name="EditValue" xml:space="preserve">
      <value>Edit value</value>
  </data>
  <data name="DeleteValue" xml:space="preserve">
      <value>Delete value</value>
  </data>
  <data name="AddValue" xml:space="preserve">
      <value>Add value</value>
  </data>
  <data name="NetWeight" xml:space="preserve">
      <value>Net weight</value>
  </data>
  <data name="NetPrice" xml:space="preserve">
      <value>Net price</value>
  </data>
  <data name="AddWeighingSlips" xml:space="preserve">
      <value>Add weighing slips</value>
  </data>
  <data name="DeliveryWeight" xml:space="preserve">
      <value>Delivery weight</value>
  </data>
  <data name="Deductions" xml:space="preserve">
      <value>Deductions</value>
  </data>
  <data name="AccountingWeight" xml:space="preserve">
      <value>Accounting weight</value>
  </data>
  <data name="CounterfoilNo" xml:space="preserve">
      <value>Counterfoil no.</value>
  </data>
  <data name="AccountingDate" xml:space="preserve">
      <value>Accounting date</value>
  </data>
  <data name="GrossAmount" xml:space="preserve">
      <value>Gross amount</value>
  </data>
  <data name="PrintPreview" xml:space="preserve">
      <value>Print preview</value>
  </data>
  <data name="Complete" xml:space="preserve">
      <value>Complete</value>
  </data>
  <data name="ContractNo" xml:space="preserve">
      <value>Contract no.</value>
  </data>
  <data name="AddContract" xml:space="preserve">
      <value>Add contract</value>
  </data>
  <data name="NoContractsConnected" xml:space="preserve">
      <value>No contracts connected</value>
  </data>
  <data name="ErrorDuringLoading" xml:space="preserve">
      <value>Error during loading</value>
  </data>
  <data name="ErrorRetrievingTheHeader" xml:space="preserve">
      <value>Error retrieving the header: {0}</value>
  </data>
  <data name="NotFound" xml:space="preserve">
      <value>Not found</value>
  </data>
  <data name="GrainAccountingWithIDNotFound" xml:space="preserve">
      <value>Grain accounting with ID {gbvNumber} not found.</value>
  </data>
  <data name="ErrorWhenRetrievingThePositions" xml:space="preserve">
      <value>Error when retrieving the positions: {0}</value>
  </data>
  <data name="SupplierWithNumberNotFound" xml:space="preserve">
      <value>Supplier with number {0} not found</value>
  </data>
  <data name="ErrorRetrievingTheSupplier" xml:space="preserve">
      <value>Error retrieving the supplier: {0}</value>
  </data>
  <data name="DataError" xml:space="preserve">
      <value>Data error</value>
  </data>
  <data name="NoSupplierNumberFoundInHeader" xml:space="preserve">
      <value>No supplier number found in the header.</value>
  </data>
  <data name="ErrorWhenRetrievingTheContract" xml:space="preserve">
      <value>Error retrieving the contract {0}: {1}</value>
  </data>
  <data name="Action" xml:space="preserve">
      <value>Action</value>
  </data>
  <data name="EditingForWeighingSlipInvoked" xml:space="preserve">
      <value>Editing for weighing slip {0} invoked</value>
  </data>
  <data name="EditingCanceled" xml:space="preserve">
      <value>Editing canceled</value>
  </data>
  <data name="ChangesForWeighingSlipSaved" xml:space="preserve">
      <value>Changes for weighing slip {0} saved</value>
  </data>
  <data name="AreYouSure" xml:space="preserve">
      <value>Are you sure?</value>
  </data>
  <data name="ReallyDeleteWeighingSlip" xml:space="preserve">
      <value>Really delete weighing slip?</value>
  </data>
  <data name="Success" xml:space="preserve">
      <value>Success</value>
  </data>
  <data name="WeighingSlipRemoved" xml:space="preserve">
      <value>Weighing slip {0} removed</value>
  </data>
  <data name="ResetForCalled" xml:space="preserve">
      <value>Reset for '{0}' called</value>
  </data>
  <data name="ValueUpdated" xml:space="preserve">
      <value>Value '{0}' updated</value>
  </data>
  <data name="UpdateFailed" xml:space="preserve">
      <value>Update failed</value>
  </data>
  <data name="ErrorDuringEditing" xml:space="preserve">
      <value>Error during editing: {0}</value>
  </data>
  <data name="Confirm" xml:space="preserve">
      <value>Confirm</value>
  </data>
  <data name="ReallyDeleteValue" xml:space="preserve">
      <value>Really delete value?</value>
  </data>
  <data name="ValueRemoved" xml:space="preserve">
      <value>Value {'0'} removed</value>
  </data>
  <data name="Info" xml:space="preserve">
      <value>Info</value>
  </data>
  <data name="NewValueAddedPlaceholder" xml:space="preserve">
      <value>New value added (placeholder)</value>
  </data>
  <data name="NewWeighingSlipAddedPlaceholder" xml:space="preserve">
      <value>New weighing slip added (placeholder)</value>
  </data>
  <data name="EnterNo" xml:space="preserve">
      <value>Enter no.</value>
  </data>
  <data name="NewValue" xml:space="preserve">
      <value>New value</value>
  </data>
  <data name="Unit" xml:space="preserve">
      <value>Unit</value>
  </data>
  <data name="TotalPriceNet" xml:space="preserve">
      <value>Total price net</value>
  </data>
  <data name="Eg" xml:space="preserve">
      <value>e.g.</value>
  </data>
  <data name="Ok" xml:space="preserve">
      <value>Ok</value>
  </data>
  <data name="NoEntriesFound" xml:space="preserve">
      <value>No entries found</value>
  </data>
  <data name="MaskNo" xml:space="preserve">
      <value>Mask no.</value>
  </data>
  <data name="ParameterType" xml:space="preserve">
      <value>Parameter type</value>
  </data>
  <data name="CalculationType" xml:space="preserve">
      <value>Calculation type</value>
  </data>
  <data name="CalculateFrom" xml:space="preserve">
      <value>Calculate from</value>
  </data>
  <data name="CalculateUntil" xml:space="preserve">
      <value>Calculate until</value>
  </data>
  <data name="BaseValue" xml:space="preserve">
      <value>Base value</value>
  </data>
  <data name="StepValue" xml:space="preserve">
      <value>Step value</value>
  </data>
  <data name="Factor" xml:space="preserve">
      <value>Factor</value>
  </data>
  <data name="BaseValueFrom" xml:space="preserve">
      <value>Base value from</value>
  </data>
  <data name="BaseValueTo" xml:space="preserve">
      <value>Base value to</value>
  </data>
  <data name="ModifierValueStart" xml:space="preserve">
      <value>Modifier value start</value>
  </data>
  <data name="ModifierValueStep" xml:space="preserve">
      <value>Modifier value step</value>
  </data>
  <data name="SavingFailed" xml:space="preserve">
      <value>Saving failed</value>
  </data>
  <data name="AllFieldsMustBeFilled" xml:space="preserve">
      <value>All fields must be filled.</value>
  </data>
  <data name="Update" xml:space="preserve">
      <value>Update</value>
  </data>
  <data name="NoAccountingFound" xml:space="preserve">
      <value>No accounting found</value>
  </data>
  <data name="Open" xml:space="preserve">
      <value>Open</value>
  </data>
  <data name="Price" xml:space="preserve">
      <value>Price</value>
  </data>
  <data name="ErrorDuringLoadingTheData" xml:space="preserve">
      <value>Error during loading the data: {0}</value>
  </data>
  <data name="GrainParameters" xml:space="preserve">
      <value>Grain parameters</value>
  </data>
  <data name="MaskType" xml:space="preserve">
      <value>Mask type</value>
  </data>
  <data name="Humidity" xml:space="preserve">
      <value>Humidity</value>
  </data>
  <data name="Money" xml:space="preserve">
      <value>Money</value>
  </data>
  <data name="Trimming" xml:space="preserve">
      <value>Trimming</value>
  </data>
  <data name="Import" xml:space="preserve">
      <value>Import</value>
  </data>
  <data name="OpenAPI" xml:space="preserve">
      <value>OpenAPI</value>
  </data>
  <data name="Home" xml:space="preserve">
      <value>Home</value>
  </data>
  <data name="NoOrderImported" xml:space="preserve">
      <value>No order imported</value>
  </data>
  <data name="OrderNumber" xml:space="preserve">
      <value>Order number</value>
  </data>
  <data name="PurchaseOrderNumber" xml:space="preserve">
      <value>Purchase order number</value>
  </data>
  <data name="OrderDate" xml:space="preserve">
      <value>Order date</value>
  </data>
  <data name="DeliveryFromDate" xml:space="preserve">
      <value>Delivery from date</value>
  </data>
  <data name="DeliveryToDate" xml:space="preserve">
      <value>Delivery to date</value>
  </data>
  <data name="ImportOfOrdersWasSuccessfullyCompleted" xml:space="preserve">
      <value>The import of {0} orders was successfully completed. Duration: {1}.</value>
  </data>
  <data name="Commission" xml:space="preserve">
      <value>Commissioning</value>
  </data>
  <data name="Printer" xml:space="preserve">
      <value>Printer</value>
  </data>
  <data name="WithRobot" xml:space="preserve">
      <value>With Robot</value>
  </data>
  <data name="SelectCommissionTrack" xml:space="preserve">
      <value>Select Commissiontrack</value>
  </data>
  <data name="PrintSettings" xml:space="preserve">
      <value>Printer Settings</value>
  </data>
  <data name="GoodsRecipient" xml:space="preserve">
      <value>Goods Recipient</value>
  </data>
  <data name="PalletCount" xml:space="preserve">
      <value>Pallet Count</value>
  </data>
  <data name="Customer" xml:space="preserve">
      <value>Customer</value>
  </data>
  <data name="CommissionButton" xml:space="preserve">
      <value>Commission</value>
  </data>
  <data name="PalletTypeIsMandatory" xml:space="preserve">
      <value>Pallet type is mandatory</value>
  </data>
  <data name="PriceList" xml:space="preserve">
      <value>Price list</value>
  </data>
  <data name="PriceInKG" xml:space="preserve">
      <value>Price in KG</value>
  </data>
  <data name="InPriceList" xml:space="preserve">
      <value>In price list</value>
  </data>
  <data name="ShouldTheArticleReallyBeDeleted" xml:space="preserve">
      <value>Should the article '{0}' really be deleted?</value>
  </data>
  <data name="DeleteArticle" xml:space="preserve">
      <value>Delete article</value>
  </data>
  <data name="Articles" xml:space="preserve">
      <value>Articles</value>
  </data>
  <data name="PriceListCouldNotBeSaved" xml:space="preserve">
      <value>Price list could not be saved</value>
  </data>
  <data name="PriceListSaved" xml:space="preserve">
      <value>Price list saved</value>
  </data>
  <data name="CreateArticle" xml:space="preserve">
      <value>Create article</value>
  </data>
  <data name="LoadArticles" xml:space="preserve">
      <value>Load articles</value>
  </data>
  <data name="SearchForArticlesByArticleNumber" xml:space="preserve">
      <value>Search for articles by article number...</value>
  </data>
  <data name="ArticleCouldNotBeDeletedBecauseItIsUsedInAnotherTable" xml:space="preserve">
      <value>Article could not be deleted because it is used in another table</value>
  </data>
  <data name="ArticleHasBeenDeleted" xml:space="preserve">
      <value>Article has been deleted</value>
  </data>
  <data name="ArticleBasicData" xml:space="preserve">
      <value>Article Basic Data</value>
  </data>
  <data name="MatchcodeIsMandatory" xml:space="preserve">
      <value>Matchcode is mandatory</value>
  </data>
  <data name="DesignationIsMandatory" xml:space="preserve">
      <value>Designation is mandatory</value>
  </data>
  <data name="Add" xml:space="preserve">
      <value>Add</value>
  </data>
  <data name="AdditionalInfo" xml:space="preserve">
      <value>Additional info</value>
  </data>
  <data name="PriceInfo" xml:space="preserve">
      <value>Price info</value>
  </data>
  <data name="Tags" xml:space="preserve">
      <value>Tags</value>
  </data>
  <data name="CashAccount" xml:space="preserve">
      <value>Cash account</value>
  </data>
  <data name="FinancialAccounting" xml:space="preserve">
      <value>Financial accounting</value>
  </data>
  <data name="PurchasePrice" xml:space="preserve">
      <value>Purchase price</value>
  </data>
  <data name="PurchasePriceGross" xml:space="preserve">
      <value>Purchase price (gross)</value>
  </data>
  <data name="RetailPrice" xml:space="preserve">
      <value>Retail price</value>
  </data>
  <data name="RetailPriceGross" xml:space="preserve">
      <value>Retail price (gross)</value>
  </data>
  <data name="Group" xml:space="preserve">
      <value>Group</value>
  </data>
  <data name="ArticleCouldNotBeSaved" xml:space="preserve">
      <value>Article could not be saved</value>
  </data>
  <data name="ArticleSaved" xml:space="preserve">
      <value>Article saved</value>
  </data>
  <data name="NoArticleFound" xml:space="preserve">
      <value>No article found</value>
  </data>
  <data name="NoArticleWithArticleNumberFound" xml:space="preserve">
      <value>No article with article number {0} found</value>
  </data>
</root>