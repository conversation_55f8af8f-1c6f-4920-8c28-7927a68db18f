using System.Text;
using System.Xml;
using System.Xml.Schema;
using System.Xml.Serialization;

namespace ObjectDeAndSerialize;

public static class XmlUtilities
{
    public static string SerializeToXml(this object input)
    {
        return new StreamReader(SerializeToXmlMemoryStream(input)).ReadToEnd();
    }
    
    public static MemoryStream SerializeToXmlMemoryStream(this object input)
    {
        var ser = new XmlSerializer(input.GetType());

        var memStm = new MemoryStream();
        ser.Serialize(memStm, input);

        memStm.Position = 0;
        return memStm;
    } 
    
    
    public static string SerializeToXmlWithEmptyNamespace(this object input)
    {
        var xmlSerializerNamespaces = new XmlSerializerNamespaces();
        xmlSerializerNamespaces.Add("", "");
        
        var settings = new XmlWriterSettings
        {
            Indent = false,         // Keine Einrückungen
            OmitXmlDeclaration = true // XML-Deklaration nicht einfügen
        };
        return SerializeToXml(input, xmlSerializerNamespaces, settings);
    }
    
    public static string SerializeToXml(this object input, 
                                        XmlSerializerNamespaces? xmlSerializerNamespaces,
                                        XmlWriterSettings? xmlWriterSettings = null)
    {
        var xmlSerializer = new XmlSerializer(input.GetType());
        using var extentedStringWriter = new ExtentedstringWriter(new StringBuilder(), Encoding.UTF8);

        if (xmlWriterSettings is null)
        {
            xmlSerializer.Serialize(extentedStringWriter, input, xmlSerializerNamespaces);
        }
        else
        {
            using var writer = XmlWriter.Create(extentedStringWriter, xmlWriterSettings);
            xmlSerializer.Serialize(writer, input, xmlSerializerNamespaces);
        }
        
        return extentedStringWriter.ToString();
    }
    
    public static XmlDocument SerializeToXmlDocument<T>(T objectData)
    {
        using(MemoryStream memStm = new MemoryStream())
        {
            XmlSerializer ser = new XmlSerializer(typeof(T));
            ser.Serialize(memStm, objectData);

            memStm.Position = 0;

            var settings = new XmlReaderSettings
            {
                IgnoreWhitespace = true
            };

            using(var xtr = XmlReader.Create(memStm, settings))
            {  
                XmlDocument xd = new XmlDocument();
                xd.Load(xtr);

                return xd;
            }
        }
    }
    

            
    public static T? DeserializeObject<T>(this string xml)
    {
        var serializer = new XmlSerializer(typeof(T));

        using TextReader reader = new StringReader(xml);
        return (T?)serializer.Deserialize(reader);
    }
    
    public static T? DeserializeObject<T>(this XmlDocument xmlDocument)
    {
       return xmlDocument.OuterXml.DeserializeObject<T>();
    }
        
    public static string EscapeXmlContent(this string unescapedXml)
    {
        return unescapedXml.Replace("$","");
    }
    
    private sealed class ExtentedstringWriter(StringBuilder builder, Encoding desiredEncoding)
        : StringWriter(builder)
    {
        public override Encoding Encoding => desiredEncoding;
    }
    

    public static void XsdValidationEventHandler(object? sender, ValidationEventArgs e)
    {
        switch (e.Severity)
        {
            case XmlSeverityType.Warning:
                Console.Write("WARNING: ");
                Console.WriteLine(e.Message);
                break;
            case XmlSeverityType.Error:
                Console.Write("ERROR: ");
                Console.WriteLine(e.Message);
                break;
            default:
                throw new ArgumentOutOfRangeException();
        }
    }
}