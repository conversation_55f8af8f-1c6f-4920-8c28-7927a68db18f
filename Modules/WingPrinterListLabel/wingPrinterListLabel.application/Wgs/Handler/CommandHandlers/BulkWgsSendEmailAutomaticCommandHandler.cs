using MediatR;
using Microsoft.Extensions.Logging;
using PrinterService;
using PrinterService.Models;
using WingCore.application.Contract;
using WingCore.application.Contract.IModels;
using WingCore.application.Contract.Services;
using WingCore.application.Lieferant;
using WingCore.application.PrintableDtos;
using WingCore.application.Wiegeschein;
using WingCore.domain.Common.Configurations;
using WingCore.domain.Common.Flags;
using wingPrinterListLabel.application.Contracts;
using wingPrinterListLabel.application.Wgs.Helpers;

namespace wingPrinterListLabel.application.Wgs.Handler.CommandHandlers;

public class BulkWgsSendEmailAutomaticCommandHandler(ISender mediatr,
                                                     IWgsRepository wgsRepository,
                                                     IApplicationPath applicationPath,
                                                     IConfigurationService configurationService,
                                                     IListLabelRepository lListLabelRepository,
                                                     IUnitOfWork iUnitOfWork,
                                                     ICountryService countryService,
                                                     ILogger<BulkWgsSendEmailAutomaticCommandHandler> logger) : IRequestHandler<BulkWgsSendEmailAutomaticCommand, BulkWgsSendEmailResult>
{
    public async Task<BulkWgsSendEmailResult> Handle(BulkWgsSendEmailAutomaticCommand command, CancellationToken cancellationToken)
    {
        try
        {
            logger.LogInformation("Starting automatic WGS email sending job for language: {Language}", command.LanguageToUse);
            
            // Get all WGS that need to be sent automatically (without WgsMailSendSuccess flag)
            var wgsToSend = await mediatr.Send(new WgsToSendEmailAutomaticallyQuery(), cancellationToken);
            var wgsWithLabList = wgsToSend.ToList();

            if (!wgsWithLabList.Any())
            {
                logger.LogInformation("No WGS found that need to be sent automatically");
                return new BulkWgsSendEmailResult
                {
                    TotalProcessed = 0,
                    SuccessCount = 0,
                    FailedCount = 0,
                    SkippedCount = 0,
                    Results = []
                };
            }

            logger.LogInformation("Found {Count} WGS records to send automatically", wgsWithLabList.Count);

            // Check if EN configuration exists as fallback
            var eWgsConfigCheck = await configurationService.GetConfigurationElectricWgsAsync();
            var hasEnConfig = eWgsConfigCheck?.LanguageToEmailFields?.ContainsKey("EN") == true;
            string? globalErrorMessage = null;

            var successCount = 0;
            var failedCount = 0;
            var skippedCount = 0;
            var results = new List<WgsEmailSendResult>();

            foreach (var wgsWithLab in wgsWithLabList)
            {
                var wgsResult = new WgsEmailSendResult
                {
                    WgsNumber = wgsWithLab.Wgsnr,
                    Success = false
                };

                try
                {
                    // Get supplier and email from supplier data using Wgslfnr
                    var (supplier, eWgsEmail) = await GetSupplierAndEmailForWgs(wgsWithLab.Wgs, cancellationToken);

                    if (string.IsNullOrWhiteSpace(eWgsEmail))
                    {
                        logger.LogWarning("No email address found for WGS {WgsNumber} (Supplier: {SupplierId})", wgsWithLab.Wgsnr, wgsWithLab.Wgslfnr);
                        wgsResult = wgsResult with { ErrorMessage = "No email address found for supplier" };
                        results.Add(wgsResult);
                        skippedCount++;

                        // Set WGS flag for email send skipped to prevent retrying
                        wgsRepository.SetWgsFlag([wgsWithLab.Wgs.Id], WgsFlags.WgsMailSendSkipped);
                        continue;
                    }

                    wgsResult = wgsResult with { Email = eWgsEmail };

                    // Get the correct config based on supplier's country
                    var languageCode = await GetLanguageCodeForSupplier(supplier, cancellationToken);
                    var (eWgsConfig, emailBodyParameter) = await GetEWgsEmailConfig(languageCode);

                    // Check if configuration was found
                    if (eWgsConfig == null || emailBodyParameter == null)
                    {
                        logger.LogWarning("No email configuration found for WGS {WgsNumber} with language code {LanguageCode}. Skipping email.", wgsWithLab.Wgsnr, languageCode);
                        wgsResult = wgsResult with { ErrorMessage = $"No email configuration found for language '{languageCode}'. EN fallback config is missing." };
                        results.Add(wgsResult);
                        skippedCount++;

                        // Set global error message if EN config is missing
                        if (!hasEnConfig && globalErrorMessage == null)
                        {
                            globalErrorMessage = "Für den automatischen Wiegeschein-Versand fehlt die konfiguration 'EN'";
                        }

                        continue;
                    }

                    var emailDataToCreateEmail = new EmailDataToCreateEmail
                    {
                        PrintableObject = LabelWiegeschein.Create(wgsWithLab),
                        LayoutFileWithPath = emailBodyParameter.LayoutForPrint,
                        AppFolder = applicationPath.GetPath(),
                        EmailParameter = new EmailParameter()
                        {
                            BodyTemplate = emailBodyParameter.EmailBodyHtml,
                            FileName = emailBodyParameter.FileName,
                            Subject = emailBodyParameter.Subject,
                            
                            SmtpSecureConnection = eWgsConfig.SmtpSecureConnection,
                            SmtpSenderAddress = eWgsConfig.SmtpSenderAddress,
                            SmtpSenderName = eWgsConfig.SmtpSenderName,
                            SmtpServerAddress = eWgsConfig.SmtpServerAddress,
                            SmtpServerPort = eWgsConfig.SmtpServerPort,
                            SmtpServerUser = eWgsConfig.SmtpServerUser,
                            SmtpServerPassword = eWgsConfig.SmtpServerPassword,
                            SmtpSocketTimeout = eWgsConfig.SmtpSocketTimeout,
                            To = eWgsEmail
                        }
                    };

                    // Just attach as PDF, no XML needed for WGS
                    PrinterCreator.SetEmailDataForSendWithPdf(ref emailDataToCreateEmail,
                                                        "",
                                                        "",
                                                        lListLabelRepository);

                    // Actually send the email
                    PrinterCreator.SendEmailWithSetData(emailDataToCreateEmail);

                    // Set WGS flag for email sent success
                    wgsRepository.SetWgsFlag([wgsWithLab.Wgs.Id], WgsFlags.WgsMailSendSuccess);
                    wgsResult = wgsResult with { Success = true };
                    successCount++;

                    logger.LogInformation("Successfully sent WGS email for WGS {WgsNumber} to {Email}", wgsWithLab.Wgsnr, eWgsEmail);
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, "Failed to send WGS email for WGS {WgsNumber}", wgsWithLab.Wgsnr);
                    wgsResult = wgsResult with { ErrorMessage = ex.InnerException?.Message ?? ex.Message };
                    failedCount++;
                }

                results.Add(wgsResult);
            }

            // Save all changes
            await iUnitOfWork.SaveChangesAsync(cancellationToken);

            logger.LogInformation("Automatic WGS email job completed. Success: {SuccessCount}, Failed: {FailedCount}, Skipped: {SkippedCount}",
                                 successCount, failedCount, skippedCount);

            return new BulkWgsSendEmailResult
            {
                TotalProcessed = wgsWithLabList.Count,
                SuccessCount = successCount,
                FailedCount = failedCount,
                SkippedCount = skippedCount,
                Results = results,
                GlobalErrorMessage = globalErrorMessage
            };
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error during automatic WGS email sending job");
            throw;
        }
    }

    private async Task<(WingCore.domain.Models.Lieferanten?, string)> GetSupplierAndEmailForWgs(WingCore.domain.Models.Wg wgs, CancellationToken cancellationToken)
    {
        // Check if WGS has a supplier number
        if (wgs.Wgslfnr == null || wgs.Wgslfnr == 0)
            return (null, "");

        try
        {
            // Get supplier by number using the existing query handler
            var suppliers = await mediatr.Send(new GetLieferantByNumberQuery(wgs.Wgslfnr.Value), cancellationToken);
            var supplier = suppliers.FirstOrDefault();

            if (supplier == null)
                return (null, "");

            // Check if supplier has a valid email address (not null, not empty, not only spaces)
            if (string.IsNullOrWhiteSpace(supplier.Lfemail))
                return (supplier, "");

            return (supplier, supplier.Lfemail.Trim());
        }
        catch (Exception ex)
        {
            // Log the error but don't fail the entire batch
            logger.LogWarning(ex, "Error getting email for WGS {WgsNumber} with supplier {SupplierId}", wgs.Wgsnr, wgs.Wgslfnr);
            return (null, "");
        }
    }

    private async Task<string> GetLanguageCodeForSupplier(WingCore.domain.Models.Lieferanten? supplier, CancellationToken cancellationToken)
    {
        if (supplier == null)
            return "EN"; // Default to English for generic config

        try
        {
            // If no country is set for supplier, use EN for generic English config
            if (string.IsNullOrWhiteSpace(supplier.Lfland))
                return "EN";

            // Get the IntraKennung (two letter code) from LandKennung table
            var languageCode = await countryService.GetCountryIdByName(supplier.Lfland);

            // If no language code found, default to English
            return string.IsNullOrWhiteSpace(languageCode) ? "EN" : languageCode;
        }
        catch (Exception ex)
        {
            logger.LogWarning(ex, "Error getting language code for supplier {SupplierId} with country {Country}", supplier?.Lfnummer, supplier?.Lfland);
            return "EN"; // Default to English on error
        }
    }

    private async Task<(ConfigurationElectricWgs?, LanguageAndEmailFields?)> GetEWgsEmailConfig(string languageToUse)
    {
        var eWgsConfig = await configurationService.GetConfigurationElectricWgsAsync();
        if (eWgsConfig is null)
            throw new Exception("Es existiert keine E-Mail konfiguration für E-Wiegescheine");

        LanguageAndEmailFields? emailBodyParameter = null;
        var usedCountry = new List<string>();

        // Try the requested language first
        if (!string.IsNullOrWhiteSpace(languageToUse))
        {
            usedCountry.Add(languageToUse);
            eWgsConfig.LanguageToEmailFields.TryGetValue(languageToUse, out emailBodyParameter);
        }

        // If not found, try fallback to EN (generic English config)
        if (emailBodyParameter is null)
        {
            usedCountry.Add("EN");
            eWgsConfig.LanguageToEmailFields.TryGetValue("EN", out emailBodyParameter);
        }

        // If EN config also not found, return null to indicate configuration missing
        if (emailBodyParameter is null)
        {
            logger.LogWarning("No email configuration found for languages: {Languages}. EN fallback config is missing.", string.Join(",", usedCountry));
            return (null, null);
        }

        return (eWgsConfig, emailBodyParameter);
    }
}
