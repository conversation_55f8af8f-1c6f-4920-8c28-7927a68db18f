using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WingCore.domain.Models.Quartz;

namespace WingCore.data.EntityConfigurations.QuartzScheduler;

public class QrtzFiredTriggerConfiguration : IEntityTypeConfiguration<QrtzFiredTrigger>
{
    public void Configure(EntityTypeBuilder<QrtzFiredTrigger> builder)
    {
        builder.HasKey(e => new { e.SchedName, e.EntryId });

        builder.ToTable("QRTZ_FIRED_TRIGGERS");

        builder.Property(e => e.SchedName)
            .HasColumnType("NVARCHAR(120)")
            .HasColumnName("SCHED_NAME");

        builder.Property(e => e.EntryId)
            .HasColumnType("NVARCHAR(140)")
            .HasColumnName("ENTRY_ID");

        builder.Property(e => e.FiredTime)
            .HasColumnType("BIGINT")
            .HasColumnName("FIRED_TIME");

        builder.Property(e => e.InstanceName)
            .HasColumnType("NVARCHAR(200)")
            .HasColumnName("INSTANCE_NAME");

        builder.Property(e => e.IsNonconcurrent)
            .HasColumnName("IS_NONCONCURRENT");

        builder.Property(e => e.JobGroup)
            .HasColumnType("NVARCHAR(150)")
            .HasColumnName("JOB_GROUP");

        builder.Property(e => e.JobName)
            .HasColumnType("NVARCHAR(150)")
            .HasColumnName("JOB_NAME");

        builder.Property(e => e.Priority).HasColumnName("PRIORITY");

        builder.Property(e => e.RequestsRecovery)
            .HasColumnName("REQUESTS_RECOVERY");

        builder.Property(e => e.SchedTime)
            .HasColumnType("BIGINT")
            .HasColumnName("SCHED_TIME");

        builder.Property(e => e.State)
            .HasColumnType("NVARCHAR(16)")
            .HasColumnName("STATE");

        builder.Property(e => e.TriggerGroup)
            .HasColumnType("NVARCHAR(150)")
            .HasColumnName("TRIGGER_GROUP");

        builder.Property(e => e.TriggerName)
            .HasColumnType("NVARCHAR(150)")
            .HasColumnName("TRIGGER_NAME");
    }
}