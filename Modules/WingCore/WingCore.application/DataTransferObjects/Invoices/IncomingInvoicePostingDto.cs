using WingCore.domain;
using WingCore.domain.Common;
using WingCore.domain.Models;

namespace WingCore.application.DataTransferObjects.Invoices;

public record IncomingInvoicePostingDto : BaseInvoicePostingDto, IInvoiceCompressable<IncomingInvoicePostingDto>
{
    private void Init(Erhauptdatei erhauptdatei)
    {
        Id = erhauptdatei.Id;
        Description = erhauptdatei.Bezeichnung ?? string.Empty;
        Number = erhauptdatei.ArtNr ?? 0;
        LsNumber = erhauptdatei.Lsnr ?? 0;
        Status = erhauptdatei.Status ?? string.Empty;
        Schema = erhauptdatei.ErfSchema ?? PostingsSchema.Empty;
        Quantity = Convert.ToDecimal(erhauptdatei.Anzahl ?? 1);
        Quantity = Quantity == 0 ? 1 : Quantity;
        TotalQuantity = erhauptdatei.Gesamtmenge ?? 0;
        TotalDiscount = 0;
        TotalNetto = erhauptdatei.Gesamtnetto ?? 0;
        FibuAccount = erhauptdatei.Konto ?? string.Empty;
        GridPosition = erhauptdatei.GridPosition ?? 0;
        ReferenceSize = erhauptdatei.BzgGr ?? string.Empty;
        
        var vat = erhauptdatei.MwstProz ?? 0;
        var vatAmount =  Math.Round(TotalNettoWithDiscount *(vat / 100),4);
        TotalGross = TotalNettoWithDiscount + vatAmount;
        UnitPrice = TotalNettoWithDiscount / Quantity;
        
        Tax = new TaxDto(erhauptdatei.MwstSchl ?? 0, vatAmount,vat,TotalNettoWithDiscount, TotalGross);
    }
    
    public static implicit operator IncomingInvoicePostingDto(Erhauptdatei erhauptdatei)
    {
      var newObj = new IncomingInvoicePostingDto();
      newObj.Init(erhauptdatei);
      return newObj;
    }
    
    public void Merge(IncomingInvoicePostingDto posting)
    {
        TotalGross += posting.TotalGross;
        TotalDiscount += posting.TotalDiscount;
        TotalNetto += posting.TotalNetto;
    }
}
