using PrinterService;
using WingCore.application.DataTransferObjects.Wgs;
using WingCore.domain.Models;

namespace WingCore.application.PrintableDtos;

public class LabelWiegeschein : IPrintObject
{
    public string PrintType() => "WGS";
    public string ObjName() => "LabelWiegeschein";

    public static LabelWiegeschein Create(WgAdfinityJobDto wiegeschein)
    {
        var newObj = new LabelWiegeschein()
        {
            Wgsnr = wiegeschein.WgsNumber,
            WgsDatum = wiegeschein.WgsDatum?.ToString("d") ?? string.Empty,
            WgsLfNr = wiegeschein.WgsLfNr ?? 0,
            WgsArtNr = wiegeschein.WgsArtNr ?? 0,
            WgsPreis = wiegeschein.WgsPreis ?? 0,
            WgsMenge = wiegeschein.WgsMenge ?? 0,
            WgsGegBlgNr = wiegeschein.WgsGegBlgNr
        };
        
        return newObj;
    }

    public static LabelWiegeschein Create(Wg wiegeschein)
    {
        var newObj = new LabelWiegeschein()
        {
            Wgsnr = wiegeschein.Wgsnr,
            WgsDatum = wiegeschein.Wgsdatum?.ToString("d") ?? string.Empty,
            WgsLfNr = wiegeschein.Wgslfnr ?? 0,
            WgsArtNr = wiegeschein.WgsartNr ?? 0,
            WgsPreis = wiegeschein.Wgspreis ?? 0,
            WgsMenge = wiegeschein.Wgsmenge ?? 0,
            WgsGegBlgNr = wiegeschein.GegBlgNr ?? string.Empty,
            WgsName1 = wiegeschein.Wgsname1 ?? string.Empty,
            WgsName2 = wiegeschein.Wgsname2 ?? string.Empty,
            WgsStrasse = wiegeschein.Wgsstrasse ?? string.Empty,
            WgsPlz = wiegeschein.Wgsplz ?? string.Empty,
            WgsOrt = wiegeschein.Wgsort ?? string.Empty,
            WgsLand = wiegeschein.Wgsland ?? string.Empty,
            WgsTelefon = wiegeschein.Wgstelefon ?? string.Empty,
            WgsAnlGewicht = wiegeschein.WgsanlGewicht ?? 0,
            WgsFeuchte = wiegeschein.Wgsfeuchte ?? 0,
            WgsKontraktNr = wiegeschein.WgskontraktNr ?? 0,
            WgsZellenNr = wiegeschein.WgszellenNr ?? 0,
            WgsBemerkung = wiegeschein.Wgsbemerkung ?? string.Empty,
            WgsLkwKennz = wiegeschein.Wgslkwkennz ?? string.Empty,
            WgsLsNr = wiegeschein.WgslsNr ?? string.Empty,
            WgsEmpfaenger = wiegeschein.Wgsempfänger ?? string.Empty,
            WgsChargNr = wiegeschein.WgschargNr ?? string.Empty,
            WiegeGew = wiegeschein.WiegeGew ?? 0,
            Wiegezeit = wiegeschein.Wiegezeit?.ToString("dd.MM.yyyy HH:mm") ?? string.Empty,
            Verwieger = wiegeschein.Verwieger ?? string.Empty,

            // Additional WGS properties
            WgsAnlName2 = wiegeschein.WgsanlName2 ?? string.Empty,
            WgsAnlName3 = wiegeschein.WgsanlName3 ?? string.Empty,
            WgsAnlNr = wiegeschein.WgsanlNr ?? 0,
            WgsRmNr = wiegeschein.Wgsrmnr ?? 0, // Rückstellmuster
            WgsGetrArt = wiegeschein.WgsgetrArt ?? string.Empty,
        };

        return newObj;
    }

    public static LabelWiegeschein Create(WgsWithLabDto wgsWithLab)
    {
        var wiegeschein = wgsWithLab.Wgs;
        var wgslab = wgsWithLab.Wgslab;

        var newObj = new LabelWiegeschein()
        {
            Wgsnr = wiegeschein.Wgsnr,
            WgsDatum = wiegeschein.Wgsdatum?.ToString("d") ?? string.Empty,
            WgsLfNr = wiegeschein.Wgslfnr ?? 0,
            WgsArtNr = wiegeschein.WgsartNr ?? 0,
            WgsPreis = wiegeschein.Wgspreis ?? 0,
            WgsMenge = wiegeschein.Wgsmenge ?? 0,
            WgsGegBlgNr = wiegeschein.GegBlgNr ?? string.Empty,
            WgsName1 = wiegeschein.Wgsname1 ?? string.Empty,
            WgsName2 = wiegeschein.Wgsname2 ?? string.Empty,
            WgsStrasse = wiegeschein.Wgsstrasse ?? string.Empty,
            WgsPlz = wiegeschein.Wgsplz ?? string.Empty,
            WgsOrt = wiegeschein.Wgsort ?? string.Empty,
            WgsLand = wiegeschein.Wgsland ?? string.Empty,
            WgsTelefon = wiegeschein.Wgstelefon ?? string.Empty,
            WgsAnlGewicht = wiegeschein.WgsanlGewicht ?? 0,
            WgsFeuchte = wiegeschein.Wgsfeuchte ?? 0,
            WgsKontraktNr = wiegeschein.WgskontraktNr ?? 0,
            WgsZellenNr = wiegeschein.WgszellenNr ?? 0,
            WgsBemerkung = wiegeschein.Wgsbemerkung ?? string.Empty,
            WgsLkwKennz = wiegeschein.Wgslkwkennz ?? string.Empty,
            WgsLsNr = wiegeschein.WgslsNr ?? string.Empty,
            WgsEmpfaenger = wiegeschein.Wgsempfänger ?? string.Empty,
            WgsChargNr = wiegeschein.WgschargNr ?? string.Empty,
            WiegeGew = wiegeschein.WiegeGew ?? 0,
            Wiegezeit = wiegeschein.Wiegezeit?.ToString("dd.MM.yyyy HH:mm") ?? string.Empty,
            Verwieger = wiegeschein.Verwieger ?? string.Empty,

            // Additional WGS properties
            WgsAnlName2 = wiegeschein.WgsanlName2 ?? string.Empty,
            WgsAnlName3 = wiegeschein.WgsanlName3 ?? string.Empty,
            WgsAnlNr = wiegeschein.WgsanlNr ?? 0,
            WgsRmNr = wiegeschein.Wgsrmnr ?? 0, // Rückstellmuster
            WgsGetrArt = wiegeschein.WgsgetrArt ?? string.Empty,

            // Wgslab properties (if available)
            WgslabBez1 = wgslab?.WgslabBez1 ?? string.Empty,
            WgslabBez2 = wgslab?.WgslabBez2 ?? string.Empty,
            WgslabBez3 = wgslab?.WgslabBez3 ?? string.Empty,
            WgslabBez4 = wgslab?.WgslabBez4 ?? string.Empty,
            WgslabBez5 = wgslab?.WgslabBez5 ?? string.Empty,
            WgslabBez6 = wgslab?.WgslabBez6 ?? string.Empty,
            WgslabBez7 = wgslab?.WgslabBez7 ?? string.Empty,
            WgslabBez8 = wgslab?.WgslabBez8 ?? string.Empty,
            WgslabBez9 = wgslab?.WgslabBez9 ?? string.Empty,
            WgslabBez10 = wgslab?.WgslabBez10 ?? string.Empty,
            WgslabBez11 = wgslab?.WgslabBez11 ?? string.Empty,
            WgslabBez12 = wgslab?.WgslabBez12 ?? string.Empty,
            WgslabWert1 = wgslab?.WgslabWert1 ?? string.Empty,
            WgslabWert2 = wgslab?.WgslabWert2 ?? string.Empty,
            WgslabWert3 = wgslab?.WgslabWert3 ?? string.Empty,
            WgslabWert4 = wgslab?.WgslabWert4 ?? string.Empty,
            WgslabWert5 = wgslab?.WgslabWert5 ?? string.Empty,
            WgslabWert6 = wgslab?.WgslabWert6 ?? string.Empty,
            WgslabWert7 = wgslab?.WgslabWert7 ?? string.Empty,
            WgslabWert8 = wgslab?.WgslabWert8 ?? string.Empty,
            WgslabWert9 = wgslab?.WgslabWert9 ?? string.Empty,
            WgslabWert10 = wgslab?.WgslabWert10 ?? string.Empty,
            WgslabWert11 = wgslab?.WgslabWert11 ?? string.Empty,
            WgslabWert12 = wgslab?.WgslabWert12 ?? string.Empty,
            WgslabEh1 = wgslab?.WgslabEh1 ?? string.Empty,
            WgslabEh2 = wgslab?.WgslabEh2 ?? string.Empty,
            WgslabEh3 = wgslab?.WgslabEh3 ?? string.Empty,
            WgslabEh4 = wgslab?.WgslabEh4 ?? string.Empty,
            WgslabEh5 = wgslab?.WgslabEh5 ?? string.Empty,
            WgslabEh6 = wgslab?.WgslabEh6 ?? string.Empty,
            WgslabEh7 = wgslab?.WgslabEh7 ?? string.Empty,
            WgslabEh8 = wgslab?.WgslabEh8 ?? string.Empty,
            WgslabEh9 = wgslab?.WgslabEh9 ?? string.Empty,
            WgslabEh10 = wgslab?.WgslabEh10 ?? string.Empty,
            WgslabEh11 = wgslab?.WgslabEh11 ?? string.Empty,
            WgslabEh12 = wgslab?.WgslabEh12 ?? string.Empty,
            ZusTxtInh1 = wgslab?.ZusTxtInh1 ?? string.Empty,
            ZusTxtInh2 = wgslab?.ZusTxtInh2 ?? string.Empty,
            ZusTxtInh3 = wgslab?.ZusTxtInh3 ?? string.Empty,
            ZusTxtInh4 = wgslab?.ZusTxtInh4 ?? string.Empty,
            ZusTxtInh5 = wgslab?.ZusTxtInh5 ?? string.Empty,
            ZusTxtInh6 = wgslab?.ZusTxtInh6 ?? string.Empty,
            ZusTxtBez1 = wgslab?.ZusTxtBez1 ?? string.Empty,
            ZusTxtBez2 = wgslab?.ZusTxtBez2 ?? string.Empty,
            ZusTxtBez3 = wgslab?.ZusTxtBez3 ?? string.Empty,
            ZusTxtBez4 = wgslab?.ZusTxtBez4 ?? string.Empty,
            ZusTxtBez5 = wgslab?.ZusTxtBez5 ?? string.Empty,
            ZusTxtBez6 = wgslab?.ZusTxtBez6 ?? string.Empty
        };

        return newObj;
    }

    public static LabelWiegeschein Dummy()
    {
        var newObj = new LabelWiegeschein
        {
            Wgsnr = 99999999,
            WgsDatum = DateTime.Now.ToString("d"),
            WgsLfNr = 12345,
            WgsName1 = "Dummy Lieferant 1",
            WgsName2 = "Dummy Lieferant 2",
            WgsStrasse = "Dummy Straße 123",
            WgsPlz = "12345",
            WgsOrt = "Dummy Stadt",
            WgsLand = "DE",
            WgsTelefon = "0123456789",
            WgsArtNr = 4711,
            WgsPreis = 250.50m,
            WgsMenge = 1000.75m,
            WgsAnlGewicht = 1050.25m,
            WgsFeuchte = 14.5m,
            WgsKontraktNr = 987654,
            WgsZellenNr = 5,
            WgsBemerkung = "Dummy Bemerkung für Wiegeschein",
            WgsLkwKennz = "AB-CD 1234",
            WgsLsNr = "LS-98765",
            WgsGegBlgNr = "GBL-12345",
            WgsEmpfaenger = "Dummy Empfänger",
            WgsChargNr = "CH-2024-001",
            WiegeGew = 1000.00m,
            Wiegezeit = DateTime.Now.ToString("dd.MM.yyyy HH:mm"),
            Verwieger = "Max Mustermann",

            // Additional WGS properties
            WgsAnlName2 = "Dummy Anlage Name 2",
            WgsAnlName3 = "Dummy Anlage Name 3",
            WgsAnlNr = 12345,
            WgsRmNr = 67890, // Rückstellmuster
            WgsGetrArt = "Weizen"
        };

        return newObj;
    }

    private string FileName { get; set; } = string.Empty;
    public string GetNameForCrationFile() => FileName;
    public string GetBeginNameFromFileList() => "WGS";
    public string GetEndNameFromFileList() => ".lst";

    // Wiegeschein properties
    public long Wgsnr { get; set; }
    public string WgsDatum { get; set; } = string.Empty;
    public long WgsLfNr { get; set; }
    public string WgsName1 { get; set; } = string.Empty;
    public string WgsName2 { get; set; } = string.Empty;
    public string WgsStrasse { get; set; } = string.Empty;
    public string WgsPlz { get; set; } = string.Empty;
    public string WgsOrt { get; set; } = string.Empty;
    public string WgsLand { get; set; } = string.Empty;
    public string WgsTelefon { get; set; } = string.Empty;
    public long WgsArtNr { get; set; }
    public decimal WgsPreis { get; set; }
    public decimal WgsMenge { get; set; }
    public decimal WgsAnlGewicht { get; set; }
    public decimal WgsFeuchte { get; set; }
    public long WgsKontraktNr { get; set; }
    public int WgsZellenNr { get; set; }
    public string WgsBemerkung { get; set; } = string.Empty;
    public string WgsLkwKennz { get; set; } = string.Empty;
    public string WgsLsNr { get; set; } = string.Empty;
    public string WgsGegBlgNr { get; set; } = string.Empty;
    public string WgsEmpfaenger { get; set; } = string.Empty;
    public string WgsChargNr { get; set; } = string.Empty;
    public decimal WiegeGew { get; set; }
    public string Wiegezeit { get; set; } = string.Empty;
    public string Verwieger { get; set; } = string.Empty;

    // Additional WGS properties
    public string WgsAnlName2 { get; set; } = string.Empty;
    public string WgsAnlName3 { get; set; } = string.Empty;
    public long WgsAnlNr { get; set; }
    public long WgsRmNr { get; set; } // Rückstellmuster
    public string WgsGetrArt { get; set; } = string.Empty;

    // Wgslab properties
    public string WgslabBez1 { get; set; } = string.Empty;
    public string WgslabBez2 { get; set; } = string.Empty;
    public string WgslabBez3 { get; set; } = string.Empty;
    public string WgslabBez4 { get; set; } = string.Empty;
    public string WgslabBez5 { get; set; } = string.Empty;
    public string WgslabBez6 { get; set; } = string.Empty;
    public string WgslabBez7 { get; set; } = string.Empty;
    public string WgslabBez8 { get; set; } = string.Empty;
    public string WgslabBez9 { get; set; } = string.Empty;
    public string WgslabBez10 { get; set; } = string.Empty;
    public string WgslabBez11 { get; set; } = string.Empty;
    public string WgslabBez12 { get; set; } = string.Empty;
    public string WgslabWert1 { get; set; } = string.Empty;
    public string WgslabWert2 { get; set; } = string.Empty;
    public string WgslabWert3 { get; set; } = string.Empty;
    public string WgslabWert4 { get; set; } = string.Empty;
    public string WgslabWert5 { get; set; } = string.Empty;
    public string WgslabWert6 { get; set; } = string.Empty;
    public string WgslabWert7 { get; set; } = string.Empty;
    public string WgslabWert8 { get; set; } = string.Empty;
    public string WgslabWert9 { get; set; } = string.Empty;
    public string WgslabWert10 { get; set; } = string.Empty;
    public string WgslabWert11 { get; set; } = string.Empty;
    public string WgslabWert12 { get; set; } = string.Empty;
    public string WgslabEh1 { get; set; } = string.Empty;
    public string WgslabEh2 { get; set; } = string.Empty;
    public string WgslabEh3 { get; set; } = string.Empty;
    public string WgslabEh4 { get; set; } = string.Empty;
    public string WgslabEh5 { get; set; } = string.Empty;
    public string WgslabEh6 { get; set; } = string.Empty;
    public string WgslabEh7 { get; set; } = string.Empty;
    public string WgslabEh8 { get; set; } = string.Empty;
    public string WgslabEh9 { get; set; } = string.Empty;
    public string WgslabEh10 { get; set; } = string.Empty;
    public string WgslabEh11 { get; set; } = string.Empty;
    public string WgslabEh12 { get; set; } = string.Empty;
    public string ZusTxtInh1 { get; set; } = string.Empty;
    public string ZusTxtInh2 { get; set; } = string.Empty;
    public string ZusTxtInh3 { get; set; } = string.Empty;
    public string ZusTxtInh4 { get; set; } = string.Empty;
    public string ZusTxtInh5 { get; set; } = string.Empty;
    public string ZusTxtInh6 { get; set; } = string.Empty;
    public string ZusTxtBez1 { get; set; } = string.Empty;
    public string ZusTxtBez2 { get; set; } = string.Empty;
    public string ZusTxtBez3 { get; set; } = string.Empty;
    public string ZusTxtBez4 { get; set; } = string.Empty;
    public string ZusTxtBez5 { get; set; } = string.Empty;
    public string ZusTxtBez6 { get; set; } = string.Empty;

}
