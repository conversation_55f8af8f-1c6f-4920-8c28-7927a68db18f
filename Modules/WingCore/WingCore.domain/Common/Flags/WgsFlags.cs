namespace WingCore.domain.Common.Flags;

[Flags]

public enum WgsFlags
{
    None                                   = 0x0000000000000000,
    EasiAdfinityReadyToSend                = 0x0000000000000001,
    EasiAdfinitySendSuccess                = 0x0000000000000002,
    EasiAdfinitySendFailed                 = 0x0000000000000004,
    WgsMailSendSuccess                     = 0x0000000000000008,
    WgsMailSendSkipped                     = 0x0000000000000010,
    // Flag                                = 0x0000000000000020,
    // FLag                                = 0x0000000000000040,
    // Flag                                = 0x0000000000000080,
    // FLag                                = 0x0000000000000010,
    // FLag                                = 0x0000000000000200,
    // Flag12                              = 0x0000000000000400,
    // Flag13                              = 0x0000000000000800,
    // Flag14                              = 0x0000000000001000,
    // Flag15                              = 0x0000000000002000,
    // Flag16                              = 0x0000000000004000,
    // Flag17                              = 0x0000000000008000,
    // Flag18                              = 0x0000000000010000,
    // Flag19                              = 0x0000000000020000,
    // Flag20                              = 0x0000000000040000,
    // Flag21                              = 0x0000000000080000,
    // Flag22                              = 0x0000000000100000,
    // Flag23                              = 0x0000000000200000,
    // Flag24                              = 0x0000000000400000,
    // Flag25                              = 0x0000000000800000,
    
    
    // notuse                              = 0x8000000000000000
}

public record WgsFlagsWorker : BaseFlagsWorker
{
    public WgsFlagsWorker(Func<long> getFlags, Action<long> setFlags)
        : base(getFlags, setFlags)
    {
    }
    
    public WgsFlags Flags
    {
        get => (WgsFlags)_getFlags();
        set => _setFlags((long)value);
    }
    
    public long ToLong() => (long)Flags;
    
    public void SetFlag(WgsFlags flag) => Flags = Flags.SetFlag(flag);
    public void RemoveFlag(WgsFlags flag) => Flags = Flags.RemoveFlag(flag);
}