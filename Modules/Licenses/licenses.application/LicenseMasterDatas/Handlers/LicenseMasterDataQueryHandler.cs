using licenses.application.Contracts;
using licenses.domain.LicenseMasterDatas;
using Marten;

namespace licenses.application.LicenseMasterDatas.Handlers;

public static class LicenseMasterDataQueryHandler
{
    public static async Task<LicenseMasterData?> Handle(LicenseMasterDataQuery query,
                                                        ILicenseMasterDataRepository repository,
                                                        CancellationToken cancellationToken)
    {
        return await repository.GetAsync(query.LicenceMasterDataId, cancellationToken);
    }

    public static async Task<LicenseMasterData?> Handle(LicenseMasterDataOverKuerzelQuery @event, 
                                                       ILicenseMasterDataRepository repository)
    {
        return await repository.GetByKuerzelAsync(@event.LicenceMasterKuerzel);
    }
}