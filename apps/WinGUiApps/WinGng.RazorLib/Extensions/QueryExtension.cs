
using System.Text.Encodings.Web;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Primitives;
using Radzen;
using Radzen.Blazor;

namespace WinGng.RazorLib.Extensions;

public static class QueryExtensions
{
    public static IQueryCollection ToQueryCollection<T>(this RadzenDataGrid<T> radzenDataGrid, out Dictionary<string,string> columKeyToName)
    {
         var query =  new Query()
        {
            OrderBy = radzenDataGrid.Query.OrderBy,
            Filter = radzenDataGrid.Query.Filter,
            Select = string.Join(",", radzenDataGrid.ColumnsCollection.Where(c => c.GetVisible() && !string.IsNullOrEmpty(c.Property))
                .Select(c => c.Property.Contains(".") ? $"{c.Property} as {c.Property.Replace(".", "_")}" : c.Property)),
        };

        columKeyToName = radzenDataGrid.ColumnsCollection.Where(c => c.GetVisible() && !string.IsNullOrEmpty(c.Property))
            .ToDictionary(c => c.Property.Replace(".", "_"), c=> c.Title);
        
        var queryParameters = new Dictionary<string, StringValues>();

        if (query.Skip != null)
        {
            queryParameters.Add("$skip", $"{query.Skip.Value}");
        }

        if (query.Top != null)
        {
            queryParameters.Add("$top", $"{query.Top.Value}");
        }

        if (!string.IsNullOrEmpty(query.OrderBy))
        {
            queryParameters.Add("$orderBy", $"{query.OrderBy}");
        }

        if (!string.IsNullOrEmpty(query.Filter))
        {
            queryParameters.Add("$filter", $"{query.Filter}");
        }

        if (!string.IsNullOrEmpty(query.Expand))
        {
            queryParameters.Add("$expand", $"{query.Expand}");
        }

        if (!string.IsNullOrEmpty(query.Select))
        {
            queryParameters.Add("$select", $"{query.Select}");
        }

        return new QueryCollection(queryParameters);
    }
}