@using wingLager.domain.WarehouseManagementModels
@using wingLager.domain.WarehouseManagementModels.Status
@inject DialogService DialogService

<RadzenTemplateForm TItem="WmEbene" Data="@_ebeneTemp" Submit="@OnSubmit">
    <RadzenStack Gap="2rem" class="rz-p-4 rz-p-md-12">
        <RadzenRow RowGap="0.25rem">
            <RadzenColumn Size="12">
                <RadzenLabel Text="Ebene-Nummer" Style="width: 150px;" />
                <RadzenTextBox Name="number" @bind-Value="@_ebeneTemp.Number" Style="width: 100%;" />
                <RadzenRequiredValidator Component="number" Text="Ebenen Nummer darf nicht leer sein" />
                <RadzenCustomValidator Component="number" Text="Ebenen Nummer existiert bereits" Validator="() => ValidateNumber(_ebeneTemp.Number)" />
                <RadzenRegexValidator Component="number" Text="Ebene Nummer darf keine Leerzeichen enthalten" Pattern="^\S*$"/>
            </RadzenColumn>
            <RadzenColumn Size="12">
                <RadzenLabel Text="Beschreibung" Style="width: 150px;" />
                <RadzenTextBox Name="description" @bind-Value="@_ebeneTemp.Description" Style="width: 100%;" />
                <RadzenRequiredValidator Component="description" Text="Beschreibung darf nicht leer sein" />
            </RadzenColumn>
            <RadzenColumn Size="12">
                <RadzenLabel Text="MaxHöhe" Style="width: 150px;" />
                <RadzenNumeric Name="maxhoehe" @bind-Value="@_ebeneTemp.MaxHeight" Style="width: 100%;" />
                <RadzenRequiredValidator Component="maxhoehe" Text="Beschreibung darf nicht leer sein" />
            </RadzenColumn>
            <RadzenColumn Size="12">
                <RadzenLabel Text="Zusatz" Style="width: 150px;" />
                <RadzenDropDown Name="restriction" Data="_restrictions" TextProperty="@nameof(WmEbenenRestriction.value)" Multiple="false"  @bind-Value="@_ebeneTemp.Restrictions" Style="width: 100%;" />
                <RadzenRequiredValidator Component="restriction" Text="Zusatz darf nicht leer sein" />
            </RadzenColumn>
        </RadzenRow>
        <RadzenColumn Size="12">
            <RadzenButton ButtonType="ButtonType.Submit" Text="@(IsEdit ? "Speichern" : "Erstellen")"></RadzenButton>
            <RadzenButton ButtonStyle="ButtonStyle.Secondary" Text="Abbrechen" Click="@(() => DialogService.Close())" />
        </RadzenColumn>
    </RadzenStack>
</RadzenTemplateForm>

@code {
    [Parameter] public required WmFeld Feld { get; set; }
    [Parameter] public WmEbene? Ebene { get; set; } = null;
    [Parameter] public required List<string> EbenenNummerList { get; set; }
    [Parameter] public bool IsEdit { get; set; } = false;

    private readonly List<WmEbenenRestriction> _restrictions = WmEbenenRestriction.GetAll();
    private WmEbene _ebeneTemp = new();

    protected override void OnInitialized()
    {
        if (Ebene is not null)
        {
            _ebeneTemp = Ebene with { };
        }
    }

    bool ValidateNumber(string number)
    {
        return !EbenenNummerList.Contains(number);
    }
    
    void OnSubmit(WmEbene ebene)
    {
        ebene.WmFeldId = Feld.Id;
        DialogService.Close(ebene);
    }
}