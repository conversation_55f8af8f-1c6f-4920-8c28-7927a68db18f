@using Commons.ComboBoxs
@using MediatR
@using wingLager.application.ComboBoxs
@using wingLager.application.WarehouseManagement.Commands
@using wingLager.domain.WarehouseManagementModels
@using WinGng.RazorLib.Components.Pages.WareHouseManager.WmDialog
@inject ISender Sender
@inject DialogService DialogService

<RadzenDataGrid @ref="_grid" AllowFiltering="true" AllowPaging="true" PageSize="10" AllowSorting="true" TItem="WmEbene" Data="@Feld.EbenenList.ToList()" EmptyText="Keine Einträge gefunden">
    <Columns>
        <RadzenDataGridColumn TItem="WmEbene" Property="@nameof(WmEbene.Number)" Title="Ebenen Nummer"/>
        <RadzenDataGridColumn TItem="WmEbene" Property="@nameof(WmEbene.Description)" Title="Ebenen Beschreibung"/>
        <RadzenDataGridColumn TItem="WmEbene" Property="@nameof(WmEbene.MaxHeight)" Title="Maximale Höhe"/>
        <RadzenDataGridColumn TItem="WmEbene" Title="Beschränkungen">
            <Template Context="ebene">
                @ebene.Restrictions.value
            </Template>
        </RadzenDataGridColumn>
        <RadzenDataGridColumn TItem="WmEbene" Filterable="false" TextAlign="TextAlign.Right">
            <Template Context="zelle">
                <RadzenButton Icon="edit" ButtonStyle="ButtonStyle.Warning" Click="() => EditEbene(zelle, Feld)"/>
                <RadzenButton Icon="delete" ButtonStyle="ButtonStyle.Danger" Click="() => DeleteEbene(zelle, Feld, true)"/>
            </Template>
        </RadzenDataGridColumn>
    </Columns>
    <Template Context="WmEbene">
            <WmStellplaetzeTable Ebene="WmEbene"/>
    </Template>
</RadzenDataGrid>
<RadzenRow class="rz-text-align-center rz-p-5">
    <RadzenColumn Offset="3" Size="6">
        <RadzenButton Text="Neue Ebene" Icon="add_circle" ButtonStyle="ButtonStyle.Primary" Click="() => AddNewEbeneDialog(Feld)"/>
    </RadzenColumn>
</RadzenRow>

@code {
    [Parameter] public required WmFeld Feld { get; set; }
    private RadzenDataGrid<WmEbene> _grid = null!;
   
    
    protected async Task AddNewEbeneDialog(WmFeld feld)
    {
        var ebeneNummerList = feld.EbenenList.Select(z => z.Number).ToList();
        WmEbene? ebeneTemp = await DialogService.OpenAsync<EbeneCreationDialog>("Ebene hinzufügen", new Dictionary<string, object> { { "Feld", feld }, {"EbenenNummerList", ebeneNummerList} });
        if (ebeneTemp is not null)
        {
            var newEbene = await Sender.Send(new CreateWmEbeneCommand(ebeneTemp.Description, ebeneTemp.Number, ebeneTemp.WmFeldId, ebeneTemp.MaxHeight, ebeneTemp.Restrictions));
            feld.EbenenList.Add(newEbene);
        }
    }
    
    protected async Task EditEbene(WmEbene ebene, WmFeld feld)
    {
        var palletTypes = (await Sender.Send(new GetAllComboBoxByTypeQuery(ComboboxType.PalettenTyp))).Select(c => c.Key).ToList();
        var ebeneNummerList = feld.EbenenList.Select(f => f.Number).Where(n => n != ebene.Number).ToList();
        WmEbene newEbene = await DialogService.OpenAsync<EbeneCreationDialog>("Ebene bearbeiten", new Dictionary<string, object> { {"Feld", feld},{ "Ebene", ebene }, {"EbenenNummerList", ebeneNummerList} ,{ "IsEdit", true }});
        if (newEbene is not null)
        {
            await Sender.Send(new UpdateWmEbeneCommand(newEbene));
            var index = feld.EbenenList.IndexOf(ebene);
            if(index < 0 )
                return;
            feld.EbenenList[index] = newEbene;
            await _grid.Reload();
        }
    }
    
    public async Task DeleteEbene(WmEbene ebene, WmFeld feld, bool withDialog)
    {
        bool? confirm = true;
        if (withDialog)
        {
            confirm = await DialogService.Confirm("Sind Sie sicher, dass Sie diese Ebene und alles darin löschen möchten?", "Ebene löschen?", new ConfirmOptions() { OkButtonText = "Ja", CancelButtonText = "Nein" });
        }
        if (confirm is false)
            return;
        await Sender.Send(new DeleteWmEbeneCommand(ebene.Id));
        feld.EbenenList.Remove(ebene);
    }
    
    
}