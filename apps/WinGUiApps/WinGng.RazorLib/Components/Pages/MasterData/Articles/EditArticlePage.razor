@page "/editArticlePage/{ArticleNumber:long}"
@page "/createArticlePage"

@using MediatR
@using WingCore.application.Article.Queries
@using WingCore.application.DataTransferObjects.Articles
@using WinGng.RazorLib.Components.Common.LoadingPage
@using WinGng.RazorLib.Components.Layout
@inject DialogService DialogService
@inject IMediator Mediator
@inject NavigationManager NavigationManager
@inject NotificationService NotificationService
@inherits BasePage

<LoadingIndicator @ref="_loadingIndicator"
                  DoLoadDataCallback="LoadData"
                  Option="_optionsLoadConfigData" DialogService="DialogService"/>

<RadzenStack>
    <div class="d-flex gap-2 justify-content-end text-end">
        <RadzenButton class="rz-base" Click="HandleBackButtonClick">@Localizer["Cancel"]</RadzenButton>
        <RadzenButton Click="SaveChanges">@Localizer["Save"]</RadzenButton>
    </div>
    
    <!-- Three-card layout using CSS Grid -->
    <div class="three-card-layout">
        <!-- Top Left Card -->
        <div class="card top-left-card">
            <RadzenText TextStyle="TextStyle.H6" TagName="TagName.H5" class="rz-color-on-secondary-lighter">@Localizer["ArticleBasicData"]</RadzenText>
            <RadzenTemplateForm @ref="TemplateForm" Data="_articleToEdit" TItem="ArticleDto">
                <RadzenRow>
                    <RadzenColumn>
                        <RadzenFormField Text="@Localizer["Matchcode"]" Variant="Variant.Outlined" Style="width: 100%">
                            <RadzenTextBox Name="ArtSbg" @bind-Value="@_articleToEdit.ArtSbg"/>
                        </RadzenFormField>
                        <RadzenRequiredValidator Component="ArtSbg" Text="@Localizer["MatchcodeIsMandatory"]"/>
                    </RadzenColumn>
                    <RadzenColumn>
                        <RadzenFormField Text="@Localizer["Designation"]" Variant="Variant.Outlined" Style="width: 100%">
                            <RadzenTextBox Name="ArtBezText1" @bind-Value="@_articleToEdit.ArtBezText1"/>
                        </RadzenFormField>
                        <RadzenRequiredValidator Component="ArtBezText1" Text="@Localizer["DesignationIsMandatory"]"></RadzenRequiredValidator>
                    </RadzenColumn>
                </RadzenRow>
                <RadzenRow>
                    <RadzenColumn Size="6">
                        <RadzenFormField Text="@Localizer["Unit"]" Variant="Variant.Outlined" class="w-100" Style="width: 100%">
                            <RadzenDropDown Data="@_units" @bind-Value="@_selectedUnit" Change="args => ChangedSelectedUnit(args)"/>
                        </RadzenFormField>
                    </RadzenColumn>
                    <RadzenColumn Size="6">
                    </RadzenColumn>
                </RadzenRow>
            </RadzenTemplateForm>
        </div>
        
        <!-- Bottom Left Card -->
        <div class="card bottom-left-card">
        </div>
        
        <!-- Right Card -->
        <div class="card right-card">
            <RadzenText TextStyle="TextStyle.H6" TagName="TagName.H5">@Localizer["Add"]</RadzenText>
            <RadzenRow>
                <RadzenColumn>
                </RadzenColumn>
                <RadzenColumn Size="12">
                    <ToggleableSections Sections="_sections" SectionTemplates="@GetSectionTemplates()"/>
                </RadzenColumn>
                <RadzenColumn class="rz-mt-5">
                </RadzenColumn>
            </RadzenRow>
        </div>
    </div>
</RadzenStack>

<style>
    .card {
        background-color: white;
        border-radius: 6px;
        padding: 16px;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
    }
    
    .three-card-layout {
        display: grid;
        grid-template-columns: 1fr 1fr;
        grid-template-rows: 1fr 1fr;
        gap: 1rem;
        height: 85vh;
        min-height: 85vh;
    }
    
    .top-left-card {
        grid-column: 1;
        grid-row: 1;
        padding: 1rem;
        overflow-y: auto;
    }
    
    .bottom-left-card {
        grid-column: 1;
        grid-row: 2;
        padding: 1rem;
        overflow-y: auto;
    }
    
    .right-card {
        grid-column: 2;
        grid-row: 1 / span 2; /* Spans both rows */
        padding: 1rem;
        overflow-y: auto;
    }
</style>

@* ReSharper disable once FieldCanBeMadeReadOnly.Local *@
@code{
    [Parameter] public long ArticleNumber { get; set; }
    
    RadzenTemplateForm<ArticleDto> TemplateForm { get; set; } = null!;
    
    private ArticleDto _articleToEdit = new()
    {
        ArtBezugsgr = "Kg",
        ArtVkpreis = 0
    };
   
    private readonly IEnumerable<string> _units = ["Kg", "To", "dz", "Stück"];
    private readonly IEnumerable<decimal> _vatPercentages = [7, 19];
    
    private string _selectedUnit = "Kg";
    private decimal _selectedVatPercentage = 19;

    private List<Section> _sections = [];

    protected override void OnInitialized()
    {
        _sections.Add(Section.Create(Localizer["AdditionalInfo"]));
        _sections.Add(Section.Create(Localizer["PriceInfo"]));
        _sections.Add(Section.Create(Localizer["Tags"]));
        _sections.Add(Section.Create(Localizer["CashAccount"]));
        _sections.Add(Section.Create(Localizer["FinancialAccounting"]));
        _sections.Add(Section.Create(Localizer["Note"]));
    }

    private List<RenderFragment<Section>> GetSectionTemplates()  => new()
    {
        section => __builder =>
        {
            <RadzenRow>
                <RadzenColumn>
                    <RadzenFormField Text="EAN" Variant="Variant.Outlined" Style="width: 100%">
                        <RadzenNumeric @bind-Value="@_articleToEdit.ArtEan" MaxLength="13"/>
                    </RadzenFormField>
                </RadzenColumn>
                <RadzenColumn>
                    <RadzenFormField Text="@Localizer["BestBeforeDateMonth"]" Variant="Variant.Outlined" Style="width: 100%">
                        <RadzenNumeric @bind-Value="@_articleToEdit.ArtHaltbarkeit"/>
                    </RadzenFormField>
                </RadzenColumn> 
            </RadzenRow>
        },
        
        section => __builder => 
        {
            <RadzenRow>
                <RadzenColumn>
                    <RadzenFormField Text="@Localizer["PurchasePrice"]" Variant="Variant.Outlined" Style="width: 100%">
                        <RadzenNumeric @bind-Value="@_articleToEdit.ArtEkpreis" Format="#.##"/>
                    </RadzenFormField>
                </RadzenColumn>
                <RadzenColumn>
                    <RadzenFormField Text="@Localizer["PurchasePriceGross"]" Variant="Variant.Outlined" Style="width: 100%">
                        <RadzenNumeric @bind-Value="@_articleToEdit.NettoEk" Format="#.##"/>
                    </RadzenFormField>
                </RadzenColumn>
            </RadzenRow>
            <RadzenRow>
                <RadzenColumn>
                    <RadzenFormField Text="@Localizer["RetailPrice"]" Variant="Variant.Outlined" Style="width: 100%">
                        <RadzenNumeric @bind-Value="@_articleToEdit.ArtVkpreis" Format="#.##"/>
                    </RadzenFormField>
                </RadzenColumn>
                <RadzenColumn>
                    <RadzenFormField Text="@Localizer["RetailPriceGross"]" Variant="Variant.Outlined" Style="width: 100%">
                        <RadzenNumeric @bind-Value="@_articleToEdit.ArtVkbrutto" Format="#.##"/>
                    </RadzenFormField>
                </RadzenColumn>
            </RadzenRow>
            <RadzenRow>
                <RadzenColumn>
                    <RadzenFormField Text="@Localizer["VAT"]" Variant="Variant.Outlined" class="w-100" Style="width: 100%">
                        <RadzenDropDown Data="@_vatPercentages" @bind-Value="@_selectedVatPercentage" Change="args => ChangedSelectedVatPercentage(args)"/>
                    </RadzenFormField>
                </RadzenColumn>
                <RadzenColumn>
                </RadzenColumn>
            </RadzenRow>
        },
        
        section => __builder => 
        {
            <RadzenRow>
                <RadzenColumn>
                    <RadzenFormField Text="@(Localizer["Group"] + " 1")" Variant="Variant.Outlined" Style="width: 100%">
                        <RadzenNumeric @bind-Value="@_articleToEdit.ArtGrp1"/>
                    </RadzenFormField>
                </RadzenColumn>
                <RadzenColumn>
                    <RadzenFormField Text="@(Localizer["Group"] +  " 2")" Variant="Variant.Outlined" Style="width: 100%">
                        <RadzenNumeric @bind-Value="@_articleToEdit.ArtGrp2"/>
                    </RadzenFormField>
                </RadzenColumn>
                <RadzenColumn>
                    <RadzenFormField Text="@(Localizer["Group"] + " 3")" Variant="Variant.Outlined" Style="width: 100%">
                        <RadzenNumeric @bind-Value="@_articleToEdit.ArtGrp3"/>
                    </RadzenFormField>
                </RadzenColumn>
            </RadzenRow>
            <RadzenRow>
                <RadzenColumn>
                    <RadzenFormField Text="@(Localizer["Group"] + " 4")" Variant="Variant.Outlined" Style="width: 100%">
                        <RadzenNumeric @bind-Value="@_articleToEdit.ArtGrp4"/>
                    </RadzenFormField>
                </RadzenColumn>
                <RadzenColumn>
                    <RadzenFormField Text="@(Localizer["Group"] + " 5")" Variant="Variant.Outlined" Style="width: 100%">
                        <RadzenNumeric @bind-Value="@_articleToEdit.ArtGrp5"/>
                    </RadzenFormField>
                </RadzenColumn>
                <RadzenColumn>
                    <RadzenFormField Text="@(Localizer["Group"] + " 6")" Variant="Variant.Outlined" Style="width: 100%">
                        <RadzenNumeric @bind-Value="@_articleToEdit.ArtGrp6"/>
                    </RadzenFormField>
                </RadzenColumn>
            </RadzenRow>
        },
        
        section => __builder => 
        {
            <RadzenRow>
            </RadzenRow>
        },
        
        section => __builder => 
        {
            <RadzenRow>
            </RadzenRow>
        },
        
        section => __builder => 
        {
            <RadzenRow>
                <RadzenColumn>
                    <RadzenFormField Text="@Localizer["Note"]" Variant="Variant.Outlined" Style="width: 100%">
                        <RadzenTextArea @bind-Value="@_articleToEdit.Bemerkung" Rows="4"/>
                    </RadzenFormField>
                </RadzenColumn>
            </RadzenRow>
        }
    };

    readonly LoadingIndicatorOptions _optionsLoadConfigData = new(true, false, false, false, 0, 6);
    LoadingIndicator? _loadingIndicator;

    private async Task LoadData()
    {
        var articleById = await Mediator.Send(new GetArticleByNumberQuery(ArticleNumber));

        if (articleById is null)
            return;
        
        _articleToEdit = articleById;
        _selectedVatPercentage = Math.Truncate(articleById.ArtMwSt ?? 19);
    }

    private Task ToggleSection(string sectionName)
    {
        var sectionIndex = _sections.FindIndex(s => s.Name == sectionName);
        if (sectionIndex >= 0)
            _sections[sectionIndex] = _sections[sectionIndex].ToggleVisibility();
        
        return Task.CompletedTask;
    }

    private void HandleBackButtonClick()
    {
        NavigationManager.NavigateTo("/articleListPage");
    }

    private void ChangedSelectedUnit(object unit)
    {
        _articleToEdit.ArtBezugsgr = unit.ToString();
        _selectedUnit = unit.ToString()!;
    }

    private void ChangedSelectedVatPercentage(object percentage)
    {
        _articleToEdit.ArtMwSt = (decimal)percentage;
        _selectedVatPercentage = (decimal)percentage;
    }
    
    async Task SaveChanges()
    {
        if (!TemplateForm.EditContext.Validate())
            return;

        try
        {
            if (ArticleNumber == 0)
            {
                await Mediator.Send(new AddArticleCommand(_articleToEdit));
            }
            else
            {
                await Mediator.Send(new UpdateArticleCommand(_articleToEdit));
            }
        }
        catch (Exception e)
        {
            NotificationService.Notify(new NotificationMessage { 
                Severity = NotificationSeverity.Error, 
                Summary = Localizer["Error"], 
                Detail = Localizer["ArticleCouldNotBeSaved"], 
                Duration = 6000 });
            throw new Exception(e.Message);
        }
       
        NavigationManager.NavigateTo("/articleListPage");
        NotificationService.Notify(new NotificationMessage
        {
            Severity = NotificationSeverity.Success, 
            Summary = Localizer["Success"], 
            Detail = Localizer["ArticleSaved"], 
            Duration = 6000
        });
    }
}