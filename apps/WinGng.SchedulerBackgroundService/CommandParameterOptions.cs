using CommandLine;

namespace WinGng.SchedulerBackgroundService;

public class CommandParameterOptions
{
    [Option('m', "mandantnumber", Required = true, HelpText = "Mandantnumber to be passed to the worker.")]
    public string MandantNumber { get; set; } = string.Empty;
    
    [Option('c', "StammDbConfigFilePath", Required = true, HelpText = "StammDbConfigFilePath to be passed to the worker.")]
    public string StammDbConfigFilePath { get; set; } = string.Empty;
}